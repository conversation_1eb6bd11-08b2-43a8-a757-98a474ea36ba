
"use client";

import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Project, Partner, Contribution, Expense, Apartment, ApartmentStatus, ExpenseCategory, ApartmentType, Sale, Attachment, Craftsman, Intervention, PartnerPercentageOverride, Client, ClientPayment, PropertyClientAssociation } from '@/types';
import { getCloudinaryAttachments, getCloudinaryAttachmentCount, hasCloudinaryAttachments, getAttachmentBadgeStyle, getAttachmentBadgeDescription } from '@/lib/utils/attachmentUtils';
import { getContributionSourceDisplayName, isProjectSourcedContribution, aggregatePartnerContributions, getIndividualPartnerContributions } from '@/lib/utils/contributionUtils';
import AttachmentManager from '@/components/AttachmentManager';
import PaymentAttachmentManager from '@/components/PaymentAttachmentManager';
import * as firebaseService from '@/lib/firebase/firebaseService'; // Use namespaced import
import { getPartners } from '@/lib/firebase/partnerService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from '@/components/ui/checkbox'; // Import Checkbox
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import {
    Alert,
    AlertDescription,
    AlertTitle,
} from "@/components/ui/alert"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import {
    ArrowLeft, Building, MapPin, Maximize, Layers, Users, UserPlus, DollarSign, TrendingUp, TrendingDown, Percent, Receipt, Plus, PlusCircle, Pencil, AlertCircle, LinkIcon, FileIcon,
    CalendarIcon,
    XCircle,
    ArrowUpDown,
    ArrowUp,
    ArrowDown,
    Home as HomeIcon,
    Tag,
    Banknote,
    Trash2,
    MoreVertical,
    Loader2,
    Store,
    Printer,
    Image as ImageIcon,
    FileText as FileTextIcon,
    Paperclip,
    Landmark,
    ChevronLeft,
    ChevronRight,
    Download,
    PieChart as PieChartIcon,
    Settings
} from "lucide-react";
import Link from 'next/link';
import { Separator } from '@/components/ui/separator';
import AddPartnerDialog from '@/components/AddPartnerDialog';
import AddContributionDialog from '@/components/AddContributionDialog';
import AddExpenseDialog from '@/components/AddExpenseDialog';
import AddApartmentDialog from '@/components/AddApartmentDialog';
import AddSaleDialog from '@/components/AddSaleDialog';
import AttachmentViewer from '@/components/AttachmentViewer';
import AddCraftsmanDialog from '@/components/AddCraftsmanDialog';
import AddInterventionDialog from '@/components/AddInterventionDialog';
import PaymentTrackingTable from '@/components/PaymentTrackingTable';
import CraftsmenTable from '@/components/CraftsmenTable';
import ExpenseDetailsTable from '@/components/ExpenseDetailsTable';
import ProjectAdminDocuments from '@/components/ProjectAdminDocuments';
import PartnerSummaryTable from '@/components/PartnerSummaryTable';
import DeleteContributionDialog from '@/components/DeleteContributionDialog';
import PartnerPercentageOverridePanel from '@/components/PartnerPercentageOverridePanel';
import { ClientPaymentTrackingDashboard } from '@/components/ClientPaymentTrackingDashboard';
import { ClientManagementTable } from '@/components/ClientManagementTable';
import { ClientPaymentTrackingTable } from '@/components/ClientPaymentTrackingTable';
import { AddClientPaymentDialog } from '@/components/AddClientPaymentDialog';
import MissingDocumentsDialog from '@/components/MissingDocumentsDialog';

import { useToast } from "@/hooks/use-toast";
import { handleAuthRedirect } from '@/lib/utils/route-utils';
import { Skeleton } from "@/components/ui/skeleton";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  type ChartConfig,
} from "@/components/ui/chart"
import { PieChart, Pie, Cell } from "recharts"
import { useSettings } from '@/contexts/SettingsContext';
import { format, parseISO, isValid, isAfter, isBefore, isEqual, startOfDay, endOfDay } from "date-fns";
import { fr, arSA, enUS, type Locale } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { useAuth, useIsEditor, useIsSuperUser, useIsAdmin } from '@/contexts/AuthContext';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/hooks/use-mobile';


const locales: { [key: string]: Locale } = {
    fr: fr,
    ar: arSA,
    en: enUS,
};

const DATE_FORMAT = "dd/MM/yyyy";
const PRINT_DATE_FORMAT = "dd/MM/yy"; // Compact format for printing

// High-contrast, accessibility-compliant color palette for pie charts
const chartColors = [
    '#1f77b4', // Strong Blue
    '#ff7f0e', // Vibrant Orange
    '#2ca02c', // Forest Green
    '#d62728', // Strong Red
    '#9467bd', // Purple
    '#8c564b', // Brown
    '#e377c2', // Pink
    '#7f7f7f', // Gray
    '#bcbd22', // Olive
    '#17becf', // Cyan
    '#aec7e8', // Light Blue
    '#ffbb78', // Light Orange
    '#98df8a', // Light Green
    '#ff9896', // Light Red
    '#c5b0d5', // Light Purple
    '#c49c94', // Light Brown
    '#f7b6d2', // Light Pink
    '#c7c7c7', // Light Gray
    '#dbdb8d', // Light Olive
    '#9edae5'  // Light Cyan
];

type InvoiceStatusFilter = 'all' | 'with' | 'without';
type ApartmentStatusFilter = 'all' | ApartmentStatus;
type SaleApartmentFilter = 'all' | string;
type PaymentTypeFilter = 'all' | 'Espèce' | 'Virement' | 'Chèque' | 'Autre';


export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.projectId as string;
  const { toast } = useToast();
  const { currency, language, t } = useSettings();
  const { isAuthenticated, isLoading: isAuthLoading, userRole } = useAuth();
  const isEditor = useIsEditor();
  const isSuperUser = useIsSuperUser();
  const isAdmin = useIsAdmin();
  const isViewer = userRole === 'viewer';
  const canDeleteContributions = isAdmin || isSuperUser; // Only admin and superuser can delete
  const dateLocale = locales[language] || fr;
  const queryClient = useQueryClient();
  const isMobile = useIsMobile();

  // Helper function to get mobile-friendly tab names
  const getTabName = (key: string, isMobile: boolean) => {
    if (!isMobile) {
      return t(key);
    }
    // Try to get the short version first, fallback to regular version
    const shortKey = `${key}Short`;
    const shortTranslation = t(shortKey);
    // If the short translation is the same as the key (not found), use regular translation
    return shortTranslation !== shortKey ? shortTranslation : t(key);
  };

   useEffect(() => {
       if (!isAuthLoading && !isAuthenticated) {
           // Save the current route before redirecting to login
           handleAuthRedirect(`/projects/${projectId}`);
           router.replace('/login');
       }
   }, [isAuthenticated, isAuthLoading, router, projectId]);

  const { data: project, isLoading: isProjectLoading, error: projectError, refetch: refetchProject } = useQuery<Project | null, Error>({
    queryKey: ['project', projectId],
    queryFn: () => firebaseService.getProjectById(projectId),
    enabled: !!projectId && isAuthenticated,
  });

  // Query for centralized partners (company-wide)
  const { data: centralizedPartners = [] } = useQuery<Partner[], Error>({
    queryKey: ['partners', 'default'],
    queryFn: () => getPartners('default'),
    enabled: isAuthenticated,
    retry: false, // Disable retries to prevent infinite loops on permission errors
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  // Query for partner percentage override data
  const { data: percentageOverrideData } = useQuery<PartnerPercentageOverride | null, Error>({
    queryKey: ['partnerPercentageOverride', projectId],
    queryFn: () => firebaseService.getPartnerPercentageOverride(projectId),
    enabled: !!projectId && isAuthenticated,
  });

  const { data: categories = [], isLoading: isLoadingCategories } = useQuery<ExpenseCategory[], Error>({
        queryKey: ['expenseCategories'],
        queryFn: firebaseService.getExpenseCategories,
        staleTime: Infinity,
        enabled: isAuthenticated,
    });

    useEffect(() => {
        if (!isAuthenticated) return;
        const unsubscribe = firebaseService.onExpenseCategoriesUpdate((updatedCategories) => {
            queryClient.setQueryData(['expenseCategories'], updatedCategories);
        });
        return () => unsubscribe();
    }, [queryClient, isAuthenticated]);

  const [isAddPartnerOpen, setIsAddPartnerOpen] = useState(false);
  const [isAddContributionOpen, setIsAddContributionOpen] = useState(false);
  const [isAddExpenseOpen, setIsAddExpenseOpen] = useState(false);
  const [isAddApartmentOpen, setIsAddApartmentOpen] = useState(false);
  const [isAddSaleOpen, setIsAddSaleOpen] = useState(false);
  const [isEditContributionOpen, setIsEditContributionOpen] = useState(false);
  const [isEditExpenseOpen, setIsEditExpenseOpen] = useState(false);
  const [isEditApartmentOpen, setIsEditApartmentOpen] = useState(false);
  const [editingContribution, setEditingContribution] = useState<Contribution | undefined>(undefined);
  const [editingExpense, setEditingExpense] = useState<Expense | undefined>(undefined);
  const [editingApartment, setEditingApartment] = useState<Apartment | undefined>(undefined);
  const [isDeleteContributionOpen, setIsDeleteContributionOpen] = useState(false);
  const [contributionToDelete, setContributionToDelete] = useState<Contribution | null>(null);
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
  const [isDeleteApartmentOpen, setIsDeleteApartmentOpen] = useState(false);
  const [apartmentToDelete, setApartmentToDelete] = useState<Apartment | null>(null);
  const [isDeleteSaleOpen, setIsDeleteSaleOpen] = useState(false);
  const [saleToDelete, setSaleToDelete] = useState<Sale | null>(null);
  const [isEditSaleOpen, setIsEditSaleOpen] = useState(false);
  const [editingSale, setEditingSale] = useState<Sale | undefined>(undefined);
  const [isMissingDocsDialogOpen, setIsMissingDocsDialogOpen] = useState(false);

  // Partner percentage override state
  const [partnerPercentageOverride, setPartnerPercentageOverride] = useState<PartnerPercentageOverride | null>(null);

  // Payment tracking state
  const [isAddCraftsmanOpen, setIsAddCraftsmanOpen] = useState(false);
  const [isAddInterventionOpen, setIsAddInterventionOpen] = useState(false);
  const [isEditCraftsmanOpen, setIsEditCraftsmanOpen] = useState(false);
  const [isEditInterventionOpen, setIsEditInterventionOpen] = useState(false);
  const [editingCraftsman, setEditingCraftsman] = useState<Craftsman | undefined>(undefined);
  const [editingIntervention, setEditingIntervention] = useState<Intervention | undefined>(undefined);
  const [selectedIntervention, setSelectedIntervention] = useState<Intervention | null>(null);
  const [selectedCraftsman, setSelectedCraftsman] = useState<Craftsman | null>(null);

  // Attachment manager state
  const [attachmentManagerOpen, setAttachmentManagerOpen] = useState(false);
  const [currentExpenseForAttachments, setCurrentExpenseForAttachments] = useState<Expense | null>(null);

  // Payment attachment manager state
  const [paymentAttachmentManagerOpen, setPaymentAttachmentManagerOpen] = useState(false);
  const [currentPaymentForAttachments, setCurrentPaymentForAttachments] = useState<ClientPayment | null>(null);

  // Client payment tracking state
  const [isAddClientPaymentOpen, setIsAddClientPaymentOpen] = useState(false);
  const [isEditClientPaymentOpen, setIsEditClientPaymentOpen] = useState(false);
  const [editingClientPayment, setEditingClientPayment] = useState<ClientPayment | undefined>(undefined);
  const [isAddPropertyClientAssociationOpen, setIsAddPropertyClientAssociationOpen] = useState(false);

  const [editingPropertyClientAssociation, setEditingPropertyClientAssociation] = useState<PropertyClientAssociation | undefined>(undefined);

  // Apartments with payments tracking (for delete validation)
  const [apartmentsWithPayments, setApartmentsWithPayments] = useState<Set<string>>(new Set());










  const [activeTab, setActiveTab] = useState('expenses');

  const [contributionFilters, setContributionFilters] = useState<{
    partnerId: string;
    startDate: Date | null;
    endDate: Date | null;
    description: string;
    type: string; // Added payment type filter
  }>({ partnerId: 'all', startDate: null, endDate: null, description: '', type: 'all' });

   const [expenseFilters, setExpenseFilters] = useState<{
    startDate: Date | null;
    endDate: Date | null;
    description: string;
    invoiceStatus: InvoiceStatusFilter;
    categoryId: string;
    type: PaymentTypeFilter;
    accountEverything: boolean;
  }>({ startDate: null, endDate: null, description: '', invoiceStatus: 'all', categoryId: 'all', type: 'all', accountEverything: false });

  // Date string states for manual input
  const [contributionDateStrings, setContributionDateStrings] = useState<{
    startDate: string;
    endDate: string;
  }>({ startDate: '', endDate: '' });

  const [expenseDateStrings, setExpenseDateStrings] = useState<{
    startDate: string;
    endDate: string;
  }>({ startDate: '', endDate: '' });

  // Date parsing and formatting functions
  const parseDateString = (dateStr: string): Date | null => {
    if (!dateStr.trim()) return null;

    // Try to parse DD/MM/YYYY format
    const parts = dateStr.split('/');
    if (parts.length === 3) {
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
      const year = parseInt(parts[2], 10);

      if (!isNaN(day) && !isNaN(month) && !isNaN(year) &&
          day >= 1 && day <= 31 && month >= 0 && month <= 11 && year >= 1900) {
        const date = new Date(year, month, day);
        // Verify the date is valid (handles invalid dates like 31/02/2023)
        if (date.getDate() === day && date.getMonth() === month && date.getFullYear() === year) {
          return date;
        }
      }
    }
    return null;
  };

  const formatDateToString = (date: Date | null): string => {
    if (!date) return '';
    return format(date, DATE_FORMAT);
  };

  // Contribution date input handlers
  const handleContributionDateInputChange = (field: 'startDate' | 'endDate', value: string) => {
    setContributionDateStrings(prev => ({ ...prev, [field]: value }));

    const parsedDate = parseDateString(value);
    if (parsedDate || value === '') {
      handleContributionFilterChange(field, parsedDate);
    }
  };

  const handleContributionDateSelect = (field: 'startDate' | 'endDate', date: Date | undefined) => {
    const selectedDate = date || null;
    handleContributionFilterChange(field, selectedDate);
    setContributionDateStrings(prev => ({ ...prev, [field]: formatDateToString(selectedDate) }));
  };

  // Expense date input handlers
  const handleExpenseDateInputChange = (field: 'startDate' | 'endDate', value: string) => {
    setExpenseDateStrings(prev => ({ ...prev, [field]: value }));

    const parsedDate = parseDateString(value);
    if (parsedDate || value === '') {
      handleExpenseFilterChange(field, parsedDate);
    }
  };

  const handleExpenseDateSelect = (field: 'startDate' | 'endDate', date: Date | undefined) => {
    const selectedDate = date || null;
    handleExpenseFilterChange(field, selectedDate);
    setExpenseDateStrings(prev => ({ ...prev, [field]: formatDateToString(selectedDate) }));
  };

  // Sync date strings with filter dates
  useEffect(() => {
      setContributionDateStrings({
          startDate: formatDateToString(contributionFilters.startDate),
          endDate: formatDateToString(contributionFilters.endDate)
      });
  }, [contributionFilters.startDate, contributionFilters.endDate]);

  useEffect(() => {
      setExpenseDateStrings({
          startDate: formatDateToString(expenseFilters.startDate),
          endDate: formatDateToString(expenseFilters.endDate)
      });
  }, [expenseFilters.startDate, expenseFilters.endDate]);

  // Load apartments with payments when project data changes (only for superusers)
  useEffect(() => {
      const loadApartmentsWithPayments = async () => {
          if (!project?.apartments || !isSuperUser) {
              // Only superusers can delete apartments, so only load this data for them
              return;
          }

          const apartmentIds = project.apartments.map(apt => apt.id);
          const apartmentsWithPaymentsSet = new Set<string>();

          // Check each apartment for payments
          for (const apartmentId of apartmentIds) {
              try {
                  const hasPayments = await firebaseService.checkApartmentHasPayments(projectId, apartmentId);
                  if (hasPayments) {
                      apartmentsWithPaymentsSet.add(apartmentId);
                  }
              } catch (error) {
                  console.error(`Error checking payments for apartment ${apartmentId}:`, error);
                  // Silently fail - if we can't check, we'll rely on server-side validation
              }
          }

          setApartmentsWithPayments(apartmentsWithPaymentsSet);
      };

      loadApartmentsWithPayments();
  }, [project?.apartments, project?.clientPayments, project?.propertyClientAssociations, projectId, isSuperUser]);

   const [apartmentFilters, setApartmentFilters] = useState<{
     floor: string;
     status: ApartmentStatusFilter;
   }>({ floor: 'all', status: 'all' });

    const [saleFilters, setSaleFilters] = useState<{
       startDate: Date | null;
       endDate: Date | null;
       clientName: string;
   }>({ startDate: null, endDate: null, clientName: '' });

  type SortableContributionColumn = keyof Pick<Contribution, 'date' | 'amount' | 'description' | 'type'> | 'partnerName';
  const [contributionSorting, setContributionSorting] = useState<{
    column: SortableContributionColumn;
    direction: 'asc' | 'desc';
  }>({ column: 'date', direction: 'desc' });

   type SortableExpenseColumn = keyof Pick<Expense, 'date' | 'amount' | 'description' | 'type' | 'category' | 'payee' | 'checkNumber'>;
   const [expenseSorting, setExpenseSorting] = useState<{
     column: SortableExpenseColumn;
     direction: 'asc' | 'desc';
   }>({ column: 'date', direction: 'desc' });

    type SortableApartmentColumn = keyof Pick<Apartment, 'number' | 'area' | 'floor' | 'bedrooms' | 'status' | 'type' | 'declaredPrice' | 'undeclaredPrice'>;
    const [apartmentSorting, setApartmentSorting] = useState<{
      column: SortableApartmentColumn;
      direction: 'asc' | 'desc';
    }>({ column: 'type', direction: 'asc' });

    type SortableSaleColumn = keyof Pick<Sale, 'date' | 'clientName' | 'amountDeclared' | 'amountUndeclared' | 'totalAmount'> | 'apartmentNumber';
    const [saleSorting, setSaleSorting] = useState<{
        column: SortableSaleColumn;
        direction: 'asc' | 'desc';
    }>({ column: 'date', direction: 'desc' });

   const addPartnerMutation = useMutation({
     mutationFn: (newPartnerData: Omit<Partner, 'id'>) => firebaseService.addPartnerToProject(projectId, newPartnerData),
     onSuccess: (newPartner) => {
       queryClient.invalidateQueries({ queryKey: ['project', projectId] });
       toast({ title: t('addPartnerButton'), description: `${newPartner.name} ${t('partnerAddedToProject')}` });
       setIsAddPartnerOpen(false);
     },
     onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
   });

   const addContributionMutation = useMutation({
       mutationFn: (newContributionData: Omit<Contribution, 'id' | 'projectId'>) => firebaseService.addContribution(projectId, newContributionData),
       onSuccess: (newContribution) => {
           queryClient.invalidateQueries({ queryKey: ['project', projectId] });
           toast({ title: t('addContributionButton'), description: `${t('contributionOf')} ${newContribution.amount.toLocaleString(language, { style: 'currency', currency: currency })} ${t('added')}` });
           setIsAddContributionOpen(false);
       },
       onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
   });

   const updateContributionMutation = useMutation({
       mutationFn: (updatedContribution: Contribution) => firebaseService.updateContribution(projectId, updatedContribution.id, updatedContribution),
       onSuccess: (_, updatedContribution) => {
           queryClient.invalidateQueries({ queryKey: ['project', projectId] });
           toast({ title: t('editContributionDialogTitle'), description: t('contributionUpdated') });
           setIsEditContributionOpen(false);
           setEditingContribution(undefined);
       },
       onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
   });

   const deleteContributionMutation = useMutation({
       mutationFn: (contributionId: string) => firebaseService.deleteContribution(projectId, contributionId),
       onSuccess: (_, contributionId) => {
           queryClient.invalidateQueries({ queryKey: ['project', projectId] });
           toast({
               title: t('deleteContribution'),
               description: t('contributionDeletedSuccessfully')
           });
           setIsDeleteContributionOpen(false);
           setContributionToDelete(null);
       },
       onError: (error: Error) => {
           toast({
               title: t('errorTitle'),
               description: error.message,
               variant: 'destructive'
           });
       },
   });

    const addExpenseMutation = useMutation({
       mutationFn: (expenseData: Omit<Expense, 'id' | 'projectId'>) =>
           firebaseService.addExpense(projectId, expenseData),
       onSuccess: (newExpense) => {
           queryClient.invalidateQueries({ queryKey: ['project', projectId] });
           toast({ title: t('addExpenseButton'), description: `${t('expenseOf')} ${newExpense.amount.toLocaleString(language, { style: 'currency', currency: currency })} ${t('added')}` });
           setIsAddExpenseOpen(false);
       },
       onError: (error: Error) => {
            if (error.message === 'errorDocumentSizeLimitExceeded') {
                 toast({ title: t('errorTitle'), description: t('errorDocumentSizeLimitExceeded'), variant: "destructive", duration: 8000});
            } else {
                 toast({ title: t('errorTitle'), description: error.message, variant: "destructive" });
            }
        },
   });

    const updateExpenseMutation = useMutation({
        mutationFn: (expenseData: Expense) =>
            firebaseService.updateExpense(projectId, expenseData.id, expenseData),
       onSuccess: () => {
           queryClient.invalidateQueries({ queryKey: ['project', projectId] });
           toast({ title: t('editExpenseDialogTitle'), description: t('expenseUpdated') });
           setIsEditExpenseOpen(false);
           setEditingExpense(undefined);
       },
        onError: (error: Error) => {
            if (error.message === 'errorDocumentSizeLimitExceeded') {
                 toast({ title: t('errorTitle'), description: t('errorDocumentSizeLimitExceeded'), variant: "destructive", duration: 8000});
            } else {
                 toast({ title: t('errorTitle'), description: error.message, variant: "destructive" });
            }
        },
   });

    const addApartmentMutation = useMutation({
        mutationFn: (newApartmentData: Omit<Apartment, 'id' | 'projectId'>) => firebaseService.addApartment(projectId, newApartmentData),
        onSuccess: (newApartment) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('addApartmentButton'), description: t('apartmentAddedSuccess').replace('{number}', newApartment.number) });
            setIsAddApartmentOpen(false);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const updateApartmentMutation = useMutation({
        mutationFn: (updatedApartmentData: Apartment) => firebaseService.updateApartment(projectId, updatedApartmentData.id, updatedApartmentData),
        onSuccess: (_, updatedApartment) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('editApartmentDialogTitle'), description: t('apartmentUpdatedSuccess').replace('{number}', updatedApartment.number) });
            setIsEditApartmentOpen(false);
            setEditingApartment(undefined);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const deleteApartmentMutation = useMutation({
        mutationFn: async ({ apartmentId, deleteSale }: { apartmentId: string, deleteSale: boolean }) => {
            if (!isSuperUser) throw new Error(t('permissionDenied'));
            if (deleteSale) {
                await firebaseService.deleteSaleByApartmentId(projectId, apartmentId);
            }
            await firebaseService.deleteApartment(projectId, apartmentId);
            return apartmentId;
        },
        onSuccess: (deletedId) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('deleteSuccessTitle'), description: t('apartmentDeleteSuccess').replace('{id}', deletedId) });
            setIsDeleteApartmentOpen(false);
            setApartmentToDelete(null);
        },
        onError: (error: any) => {
            console.error("Error deleting apartment:", error);
            toast({ title: t('errorTitle'), description: error.message || t('apartmentDeleteError'), variant: 'destructive' });
        }
    });

    const addSaleMutation = useMutation({
        mutationFn: (newSaleData: Omit<Sale, 'id' | 'projectId' | 'totalAmount'>) => firebaseService.addSale(projectId, newSaleData),
        onSuccess: (newSale) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            const aptNumber = project?.apartments?.find(apt => apt.id === newSale.apartmentId)?.number || newSale.apartmentId;
            toast({ title: t('addSaleSuccessTitle'), description: t('addSaleSuccessDesc').replace('{aptNumber}', aptNumber).replace('{clientName}', newSale.clientName) });
            setIsAddSaleOpen(false);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const updateSaleMutation = useMutation({
        mutationFn: (saleData: Sale) => firebaseService.updateSale(projectId, saleData.id, saleData),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('editSaleSuccessTitle') || 'Sale Updated', description: t('saleUpdated') || 'Sale has been updated successfully' });
            setIsEditSaleOpen(false);
            setEditingSale(undefined);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const deleteSaleMutation = useMutation({
        mutationFn: async ({ saleId }: { saleId: string }) => {
             if (!isEditor) throw new Error(t('permissionDenied'));
             await firebaseService.deleteSale(projectId, saleId);
             return saleId;
        },
        onSuccess: (deletedId) => {
             queryClient.invalidateQueries({ queryKey: ['project', projectId] });
             toast({ title: t('deleteSuccessTitle'), description: t('saleDeleteSuccess').replace('{id}', deletedId) });
             setIsDeleteSaleOpen(false);
             setSaleToDelete(null);
        },
        onError: (error: any) => {
             console.error("Error deleting sale:", error);
             toast({ title: t('errorTitle'), description: error.message || t('saleDeleteError'), variant: 'destructive' });
        }
    });

    // Craftsman mutations
    const addCraftsmanMutation = useMutation({
        mutationFn: (newCraftsmanData: Omit<Craftsman, 'id' | 'projectId' | 'createdAt'>) =>
            firebaseService.addCraftsman(projectId, newCraftsmanData),
        onSuccess: (newCraftsman) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('addCraftsmanButton'), description: t('craftsmanAddedSuccess') });
            setIsAddCraftsmanOpen(false);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const updateCraftsmanMutation = useMutation({
        mutationFn: (craftsmanData: Craftsman) =>
            firebaseService.updateCraftsman(projectId, craftsmanData.id, craftsmanData),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('editCraftsmanDialogTitle'), description: t('craftsmanUpdatedSuccess') });
            setIsEditCraftsmanOpen(false);
            setEditingCraftsman(undefined);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const deleteCraftsmanMutation = useMutation({
        mutationFn: (craftsmanId: string) => firebaseService.deleteCraftsman(projectId, craftsmanId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('deleteSuccessTitle'), description: t('craftsmanDeletedSuccess') });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    // Intervention mutations
    const addInterventionMutation = useMutation({
        mutationFn: (newInterventionData: Omit<Intervention, 'id' | 'projectId' | 'createdAt' | 'updatedAt'>) =>
            firebaseService.addIntervention(projectId, newInterventionData),
        onSuccess: (newIntervention) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('addInterventionButton'), description: t('interventionAddedSuccess') });
            setIsAddInterventionOpen(false);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const updateInterventionMutation = useMutation({
        mutationFn: (interventionData: Intervention) =>
            firebaseService.updateIntervention(projectId, interventionData.id, interventionData),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('editInterventionDialogTitle'), description: t('interventionUpdatedSuccess') });
            setIsEditInterventionOpen(false);
            setEditingIntervention(undefined);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const deleteInterventionMutation = useMutation({
        mutationFn: (interventionId: string) => firebaseService.deleteIntervention(projectId, interventionId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('deleteSuccessTitle'), description: t('interventionDeletedSuccess') });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    // Partner percentage override mutations
    const savePartnerPercentageOverrideMutation = useMutation({
        mutationFn: (overrideData: PartnerPercentageOverride) =>
            firebaseService.updatePartnerPercentageOverride(projectId, overrideData),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            queryClient.invalidateQueries({ queryKey: ['partnerPercentageOverride', projectId] });
            toast({ title: t('saveSuccessTitle') || 'Saved', description: t('percentageOverrideSaved') || 'Partner percentage override saved successfully' });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const disablePartnerPercentageOverrideMutation = useMutation({
        mutationFn: () => firebaseService.disablePartnerPercentageOverride(projectId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            queryClient.invalidateQueries({ queryKey: ['partnerPercentageOverride', projectId] });
            toast({ title: t('disableSuccessTitle') || 'Disabled', description: t('percentageOverrideDisabled') || 'Partner percentage override disabled successfully' });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    // Client management mutations
    const addClientMutation = useMutation({
        mutationFn: (newClientData: Omit<Client, 'id' | 'projectId' | 'createdAt' | 'updatedAt'>) =>
            firebaseService.addClient(projectId, newClientData),
        onSuccess: (newClient) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('addClientSuccessTitle') || 'Client Added', description: t('clientAddedSuccess').replace('{name}', newClient.name) || `Client ${newClient.name} added successfully` });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const updateClientMutation = useMutation({
        mutationFn: (clientData: Client) => {
            // The schema transformation in AddClientDialog already handles empty string to undefined conversion
            // Just pass the data directly to the Firebase service
            const updateData: Partial<Omit<Client, 'id' | 'projectId' | 'createdAt'>> = {
                name: clientData.name,
                phone: clientData.phone,
                email: clientData.email,
                address: clientData.address,
                notes: clientData.notes,
            };

            return firebaseService.updateClient(projectId, clientData.id, updateData);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('editClientSuccessTitle') || 'Client Updated', description: t('clientUpdatedSuccess') || 'Client updated successfully' });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const deleteClientMutation = useMutation({
        mutationFn: (clientId: string) => firebaseService.deleteClient(projectId, clientId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('deleteSuccessTitle'), description: t('clientDeletedSuccess') || 'Client deleted successfully' });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    // Client payment mutations
    const addClientPaymentMutation = useMutation({
        mutationFn: (newPaymentData: Omit<ClientPayment, 'id' | 'projectId' | 'createdAt' | 'updatedAt'>) =>
            firebaseService.addClientPayment(projectId, newPaymentData),
        onSuccess: (newPayment) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            const client = project?.clients?.find(c => c.id === newPayment.clientId);
            toast({ title: t('addPaymentSuccessTitle') || 'Payment Added', description: t('paymentAddedSuccess').replace('{client}', client?.name || 'Client').replace('{amount}', newPayment.amount.toLocaleString()) || `Payment of ${newPayment.amount.toLocaleString()} added for ${client?.name || 'Client'}` });
            setIsAddClientPaymentOpen(false);
            setEditingClientPayment(undefined);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const updateClientPaymentMutation = useMutation({
        mutationFn: (paymentData: ClientPayment) =>
            firebaseService.updateClientPayment(projectId, paymentData.id, paymentData),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('editPaymentSuccessTitle') || 'Payment Updated', description: t('paymentUpdatedSuccess') || 'Payment updated successfully' });
            setIsAddClientPaymentOpen(false);
            setEditingClientPayment(undefined);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const deleteClientPaymentMutation = useMutation({
        mutationFn: (paymentId: string) => firebaseService.deleteClientPayment(projectId, paymentId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('deleteSuccessTitle'), description: t('paymentDeletedSuccess') || 'Payment deleted successfully' });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    // Property-client association mutations
    const addPropertyClientAssociationMutation = useMutation({
        mutationFn: (newAssociationData: Omit<PropertyClientAssociation, 'id' | 'projectId' | 'createdAt' | 'updatedAt' | 'totalPaid' | 'remainingBalance'>) => {
            const associationWithDefaults = {
                ...newAssociationData,
                totalPaid: 0,
                remainingBalance: newAssociationData.totalDue,
            };
            return firebaseService.addPropertyClientAssociation(projectId, associationWithDefaults);
        },
        onSuccess: (newAssociation) => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            const client = project?.clients?.find(c => c.id === newAssociation.clientId);
            const apartment = project?.apartments?.find(a => a.id === newAssociation.apartmentId);
            toast({ title: t('addAssociationSuccessTitle') || 'Association Added', description: t('associationAddedSuccess').replace('{client}', client?.name || 'Client').replace('{property}', apartment?.number || 'Property') || `Association created for ${client?.name || 'Client'} and ${apartment?.number || 'Property'}` });
            setIsAddPropertyClientAssociationOpen(false);
            setEditingPropertyClientAssociation(undefined);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const updatePropertyClientAssociationMutation = useMutation({
        mutationFn: (associationData: PropertyClientAssociation) =>
            firebaseService.updatePropertyClientAssociation(projectId, associationData.id, associationData),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('editAssociationSuccessTitle') || 'Association Updated', description: t('associationUpdatedSuccess') || 'Association updated successfully' });
            setIsAddPropertyClientAssociationOpen(false);
            setEditingPropertyClientAssociation(undefined);
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    const deletePropertyClientAssociationMutation = useMutation({
        mutationFn: (associationId: string) => firebaseService.deletePropertyClientAssociation(projectId, associationId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            toast({ title: t('deleteSuccessTitle'), description: t('associationDeletedSuccess') || 'Association deleted successfully' });
        },
        onError: (error: Error) => toast({ title: t('errorTitle'), description: error.message, variant: 'destructive' }),
    });

    type AddCategoryFormData = { categoryName: string };
    const handleAddCategory = async (data: AddCategoryFormData): Promise<string | void> => {
        const newCategoryName = data.categoryName.trim();
        if (!newCategoryName) return;
        const exists = categories.some(cat => cat.name.toLowerCase() === newCategoryName.toLowerCase());
        if (exists) {
            toast({ title: t('errorTitle'), description: t('categoryExistsError'), variant: "destructive" });
            throw new Error(t('categoryExistsError'));
        }
        try {
            const addedCategory = await firebaseService.addExpenseCategory({ name: newCategoryName });
            toast({ title: t('addCategorySuccessTitle'), description: t('addCategorySuccessDesc').replace('{name}', addedCategory.name) });
            return addedCategory.name;
        } catch (error: any) {
            console.error("Error adding category:", error);
            toast({ title: t('errorTitle'), description: error.message || t('addCategoryErrorDesc'), variant: "destructive" });
            throw error;
        }
    };

    useEffect(() => {
     const handleKeyDown = (event: KeyboardEvent) => {


        if (!isEditor) return;
       if (event.key === 'Add' &&
           document.activeElement?.tagName !== 'INPUT' &&
           document.activeElement?.tagName !== 'TEXTAREA') {
           if (!isAddPartnerOpen && !isAddContributionOpen && !isAddExpenseOpen && !isAddApartmentOpen && !isAddSaleOpen &&
               !isEditContributionOpen && !isEditExpenseOpen && !isEditApartmentOpen && !isAddCategoryOpen) {
                event.preventDefault();
                if (activeTab === 'partners') setIsAddPartnerOpen(true);
                else if (activeTab === 'contributions') setIsAddContributionOpen(true);
                else if (activeTab === 'expenses' && !isAddCategoryOpen) setIsAddExpenseOpen(true);
                else if (activeTab === 'apartments') setIsAddApartmentOpen(true);
                else if (activeTab === 'sales') setIsAddSaleOpen(true);
                else if (activeTab === 'payments') setIsAddInterventionOpen(true);
           }
       }
     };
     window.addEventListener('keydown', handleKeyDown);
     return () => window.removeEventListener('keydown', handleKeyDown);
   }, [activeTab, isAddPartnerOpen, isAddContributionOpen, isAddExpenseOpen, isAddApartmentOpen, isAddSaleOpen, isAddInterventionOpen, isEditContributionOpen, isEditExpenseOpen, isEditApartmentOpen, isAddCategoryOpen, isEditor]);

   const handleAddPartner = (newPartnerData: Omit<Partner, 'id'>) => {
     if (!isEditor) return;
     addPartnerMutation.mutate(newPartnerData);
   };

   const handleAddContribution = (newContributionData: Omit<Contribution, 'id' | 'projectId'>) => {
       if (!isEditor) return;
       addContributionMutation.mutate(newContributionData);
   };

   const handleEditContribution = (updatedContributionData: Contribution) => {
       if (!isEditor) return;
       const fullUpdatedContribution: Contribution = {
           ...(editingContribution as Contribution),
           ...updatedContributionData,
           projectId: editingContribution?.projectId || projectId
       };
       updateContributionMutation.mutate(fullUpdatedContribution);
   };

    const openEditContributionDialog = (contribution: Contribution) => {
        if (!isEditor) return;
        setEditingContribution(contribution);
        setIsEditContributionOpen(true);
    };

    const openDeleteContributionDialog = (contribution: Contribution) => {
        if (!canDeleteContributions) return;
        setContributionToDelete(contribution);
        setIsDeleteContributionOpen(true);
    };

    const handleDeleteContribution = (contributionId: string) => {
        if (!canDeleteContributions) return;
        deleteContributionMutation.mutate(contributionId);
    };

   const handleAddExpense = (newExpenseData: Omit<Expense, 'id' | 'projectId'>) => {
       if (!isEditor) return;
       addExpenseMutation.mutate(newExpenseData);
   };

    const handleEditExpense = (updatedExpenseData: Expense) => {
       if (!isEditor) return;
       const fullUpdatedExpense: Expense = {
            ...(editingExpense as Expense),
            ...updatedExpenseData,
            projectId: editingExpense?.projectId || projectId
       }
       updateExpenseMutation.mutate(fullUpdatedExpense);
    };

    const openEditExpenseDialog = (expense: Expense) => {
        if (!isEditor) return;
        setEditingExpense(expense);
        setIsEditExpenseOpen(true);
    };

    const handleAddApartment = (newApartmentData: Omit<Apartment, 'id' | 'projectId'>) => {
        if (!isEditor) return;
        addApartmentMutation.mutate(newApartmentData);
    };

    const handleEditApartment = (updatedApartmentData: Apartment) => {
        if (!isEditor) return;
        const fullUpdatedApartment: Apartment = {
            ...(editingApartment as Apartment),
            ...updatedApartmentData,
            projectId: editingApartment?.projectId || projectId
        };
        updateApartmentMutation.mutate(fullUpdatedApartment);
    };

    const openEditApartmentDialog = (apartment: Apartment) => {
        if (!isEditor) return;
        setEditingApartment(apartment);
        setIsEditApartmentOpen(true);
    };

    const openDeleteApartmentDialog = (apartment: Apartment) => {
        if (!isSuperUser) return;
        setApartmentToDelete(apartment);
        setIsDeleteApartmentOpen(true);
    };

    const handleConfirmDeleteApartment = (deleteSale: boolean) => {
        if (!apartmentToDelete || !isSuperUser || deleteApartmentMutation.isPending) return;
        deleteApartmentMutation.mutate({ apartmentId: apartmentToDelete.id, deleteSale });
    };

    const handleAddSale = (newSaleData: Omit<Sale, 'id' | 'projectId' | 'totalAmount'>) => {
        if (!isEditor) return;
        addSaleMutation.mutate(newSaleData);
    };

    const openDeleteSaleDialog = (sale: Sale) => {
        if (!isEditor) return;
        setSaleToDelete(sale);
        setIsDeleteSaleOpen(true);
    };

    const handleConfirmDeleteSale = () => {
        if (!saleToDelete || !isEditor || deleteSaleMutation.isPending) return;
        deleteSaleMutation.mutate({ saleId: saleToDelete.id });
    };

    const openEditSaleDialog = (sale: Sale) => {
        if (!isEditor) return;
        setEditingSale(sale);
        setIsEditSaleOpen(true);
    };

    const handleEditSale = (updatedSaleData: Sale) => {
        if (!isEditor) return;
        const fullUpdatedSale: Sale = {
            ...(editingSale as Sale),
            ...updatedSaleData,
            projectId: editingSale?.projectId || projectId
        };
        updateSaleMutation.mutate(fullUpdatedSale);
    };

    // Craftsman handlers
    const handleAddCraftsman = (newCraftsmanData: Omit<Craftsman, 'id' | 'projectId' | 'createdAt'>) => {
        if (!isEditor) return;
        addCraftsmanMutation.mutate(newCraftsmanData);
    };

    const handleEditCraftsman = (updatedCraftsmanData: Craftsman) => {
        if (!isEditor) return;
        const fullUpdatedCraftsman: Craftsman = {
            ...(editingCraftsman as Craftsman),
            ...updatedCraftsmanData,
            projectId: editingCraftsman?.projectId || projectId
        };
        updateCraftsmanMutation.mutate(fullUpdatedCraftsman);
    };

    const openEditCraftsmanDialog = (craftsman: Craftsman) => {
        if (!isEditor) return;
        setEditingCraftsman(craftsman);
        setIsEditCraftsmanOpen(true);
    };

    const handleDeleteCraftsman = (craftsmanId: string) => {
        if (!isEditor) return;
        deleteCraftsmanMutation.mutate(craftsmanId);
    };

    // Intervention handlers
    const handleAddIntervention = (newInterventionData: Omit<Intervention, 'id' | 'projectId' | 'createdAt' | 'updatedAt'>) => {
        if (!isEditor) return;
        addInterventionMutation.mutate(newInterventionData);
    };

    const handleEditIntervention = (updatedInterventionData: Intervention) => {
        if (!isEditor) return;
        const fullUpdatedIntervention: Intervention = {
            ...(editingIntervention as Intervention),
            ...updatedInterventionData,
            projectId: editingIntervention?.projectId || projectId
        };
        updateInterventionMutation.mutate(fullUpdatedIntervention);
    };

    const openEditInterventionDialog = (intervention: Intervention) => {
        if (!isEditor) return;
        setEditingIntervention(intervention);
        setIsEditInterventionOpen(true);
    };

    const handleDeleteIntervention = (interventionId: string) => {
        if (!isEditor) return;
        deleteInterventionMutation.mutate(interventionId);
    };

    const handleInterventionSelect = (intervention: Intervention | null) => {
        setSelectedIntervention(intervention);
    };

    const handleCraftsmanSelect = (craftsman: Craftsman | null) => {
        setSelectedCraftsman(craftsman);
        // Reset intervention selection when craftsman changes
        setSelectedIntervention(null);
    };

    // Filter interventions based on selected craftsman
    const filteredInterventions = selectedCraftsman
        ? (project?.interventions || []).filter(intervention =>
            intervention.craftsmanId === selectedCraftsman.id
          )
        : (project?.interventions || []);

    // Client payment tracking handlers
    const handleAddClient = async (clientData: Omit<Client, 'id' | 'projectId' | 'createdAt' | 'updatedAt'>): Promise<Client> => {
        const newClient = await addClientMutation.mutateAsync(clientData);
        return newClient;
    };

    // Open the add client payment dialog
    const handleAddClientPayment = () => {
        if (!isEditor) return;
        setIsAddClientPaymentOpen(true);
    };

    // Submit new client payment data
    const handleSubmitClientPayment = (paymentData: Omit<ClientPayment, 'id' | 'projectId' | 'createdAt' | 'updatedAt'>) => {
        if (!isEditor) return;
        addClientPaymentMutation.mutate(paymentData);
    };

    // Open the edit client payment dialog
    const handleEditClientPayment = (payment: ClientPayment) => {
        if (!isEditor) return;
        setEditingClientPayment(payment);
        setIsEditClientPaymentOpen(true);
    };

    // Submit updated client payment data
    const handleSubmitEditClientPayment = (updatedPaymentData: ClientPayment) => {
        if (!isEditor) return;
        updateClientPaymentMutation.mutate(updatedPaymentData);
    };

    const handleDeleteClientPayment = (payment: ClientPayment) => {
        if (!isEditor) return;
        deleteClientPaymentMutation.mutate(payment.id);
    };

    // Client management handlers
    const handleEditClient = (updatedClient: Client) => {
        if (!isEditor) return;
        updateClientMutation.mutate(updatedClient);
    };

    const handleDeleteClient = (client: Client) => {
        if (!isEditor) return;
        deleteClientMutation.mutate(client.id);
    };

    const handleAddPropertyClientAssociation = (associationData: Omit<PropertyClientAssociation, 'id' | 'projectId' | 'createdAt' | 'updatedAt' | 'totalPaid' | 'remainingBalance'>) => {
        if (!isEditor) return;
        addPropertyClientAssociationMutation.mutate(associationData);
    };

    const handleEditPropertyClientAssociation = (updatedAssociationData: PropertyClientAssociation) => {
        if (!isEditor) return;
        updatePropertyClientAssociationMutation.mutate(updatedAssociationData);
    };

    const handleDeletePropertyClientAssociation = (association: PropertyClientAssociation) => {
        if (!isEditor) return;
        deletePropertyClientAssociationMutation.mutate(association.id);
    };

    const handleContributionFilterChange = useCallback((filterName: keyof typeof contributionFilters, value: any) => {
        setContributionFilters(prev => ({ ...prev, [filterName]: value }));
    }, []);

    const resetContributionFilters = useCallback(() => {
        setContributionFilters({ partnerId: 'all', startDate: null, endDate: null, description: '', type: 'all' });
        setContributionDateStrings({ startDate: '', endDate: '' });
    }, []);

    const handleContributionSort = useCallback((column: SortableContributionColumn) => {
        setContributionSorting(prev => ({
            column,
            direction: prev.column === column && prev.direction === 'asc' ? 'desc' : 'asc',
        }));
    }, []);

    const handleExpenseFilterChange = useCallback((filterName: keyof typeof expenseFilters, value: any) => {
        setExpenseFilters(prev => ({ ...prev, [filterName]: value }));
    }, []);

    const resetExpenseFilters = useCallback(() => {
        setExpenseFilters({ startDate: null, endDate: null, description: '', invoiceStatus: 'all', categoryId: 'all', type: 'all', accountEverything: false });
        setExpenseDateStrings({ startDate: '', endDate: '' });
    }, []);

    const handleExpenseSort = useCallback((column: SortableExpenseColumn) => {
        setExpenseSorting(prev => ({
            column,
            direction: prev.column === column && prev.direction === 'asc' ? 'desc' : 'asc',
        }));
    }, []);

    const handleApartmentFilterChange = useCallback((filterName: keyof typeof apartmentFilters, value: any) => {
        setApartmentFilters(prev => ({ ...prev, [filterName]: value }));
    }, []);

    const resetApartmentFilters = useCallback(() => {
        setApartmentFilters({ floor: 'all', status: 'all' });
    }, []);

    const handleApartmentSort = useCallback((column: SortableApartmentColumn) => {
        setApartmentSorting(prev => ({
            column,
            direction: prev.column === column && prev.direction === 'asc' ? 'desc' : 'asc',
        }));
    }, []);

   const handleSaleFilterChange = useCallback((filterName: keyof typeof saleFilters, value: any) => {
       setSaleFilters(prev => ({ ...prev, [filterName]: value }));
   }, []);

   const resetSaleFilters = useCallback(() => {
       setSaleFilters({ startDate: null, endDate: null, clientName: '' });
   }, []);

    const handleSaleSort = useCallback((column: SortableSaleColumn) => {
        setSaleSorting(prev => ({
            column,
            direction: prev.column === column && prev.direction === 'asc' ? 'desc' : 'asc',
        }));
    }, []);

    const getTranslatedType = (type: 'Espèce' | 'Virement' | 'Chèque' | 'Autre') => {
      switch (type) {
        case 'Espèce': return t('contributionTypeCash');
        case 'Virement': return t('contributionTypeTransfer');
        case 'Chèque': return t('contributionTypeCheck');
        case 'Autre': return t('contributionTypeOther');
        default: return type;
      }
    };

    const getTranslatedApartmentStatus = (status: ApartmentStatus) => {
       switch (status) {
         case 'available': return t('apartmentStatusAvailable');
         case 'sold': return t('apartmentStatusSold');
         default: return status;
       }
     };

     const getTranslatedApartmentType = (type: ApartmentType) => {
         switch (type) {
             case 'appartement': return t('typeAppartement');
             case 'magasin': return t('typeMagasin');
             default: return type;
         }
     };

    // Attachment manager handlers
    const openAttachmentManager = (expense: Expense) => {
        setCurrentExpenseForAttachments(expense);
        setAttachmentManagerOpen(true);
    };

    const closeAttachmentManager = () => {
        setAttachmentManagerOpen(false);
        setCurrentExpenseForAttachments(null);
    };

    const handleAttachmentsChange = (updatedAttachments: Attachment[]) => {
        if (!currentExpenseForAttachments) return;

        // Update the expense with new attachments
        const updatedExpense = {
            ...currentExpenseForAttachments,
            attachments: updatedAttachments
        };

        // Update in the database - pass the full expense object as expected by the mutation
        updateExpenseMutation.mutate(updatedExpense);
    };

    // Payment attachment manager handlers
    const openPaymentAttachmentManager = (payment: ClientPayment) => {
        setCurrentPaymentForAttachments(payment);
        setPaymentAttachmentManagerOpen(true);
    };

    const closePaymentAttachmentManager = () => {
        setPaymentAttachmentManagerOpen(false);
        setCurrentPaymentForAttachments(null);
    };

    const handlePaymentAttachmentsChange = (updatedAttachments: any[]) => {
        if (!currentPaymentForAttachments) return;

        console.log('📎 Updating payment attachments:', {
            paymentId: currentPaymentForAttachments.id,
            attachmentCount: updatedAttachments.length,
            attachments: updatedAttachments
        });

        // Update the payment with new attachments
        const updatedPayment = {
            ...currentPaymentForAttachments,
            attachments: updatedAttachments
        };

        // Update local state immediately for UI responsiveness
        setCurrentPaymentForAttachments(updatedPayment);

        // Update in the database
        updateClientPaymentMutation.mutate(updatedPayment);
    };



   const totalContributions = project?.contributions?.reduce((sum, c) => sum + c.amount, 0) ?? 0;

   const totalExpenses = useMemo(() => {
       return project?.expenses
           ?.filter(e => expenseFilters.accountEverything || !e.unaccounted)
           .reduce((sum, e) => sum + e.amount, 0) ?? 0;
   }, [project?.expenses, expenseFilters.accountEverything]);


   // Calculate total property sales from propertyClientAssociations (total PAID, not total DUE)
   // Handle cases where user doesn't have access to payment data or data is missing
   const totalPropertySales = useMemo(() => {
       if (project?.propertyClientAssociations && project.propertyClientAssociations.length > 0) {
           return project.propertyClientAssociations
               .filter(assoc => (assoc.totalPaid ?? 0) > 0)
               .reduce((sum, assoc) => sum + (assoc.totalPaid ?? 0), 0);
       }
       // Fallback to legacy sales data if payment associations are not available
       return project?.sales?.reduce((sum, sale) => sum + sale.amount, 0) ?? 0;
   }, [project?.propertyClientAssociations, project?.sales]);

   // Use propertyClientAssociations for sales display (showing total paid amount)
   const totalSalesAmount = totalPropertySales;
   const salesCount = project?.propertyClientAssociations?.length ?? 0;

   const balance = totalContributions - totalExpenses;



   // All contributions by all partners (including from source projects) for Partners tab
   const [allContributionsByPartner, setAllContributionsByPartner] = useState<Array<{
       name: string;
       total: number;
       percentage: number;
       fill: string;
       isSourceProject?: boolean;
       sourceProjectName?: string;
   }>>([]);

   // Calculate all partner contributions including source projects
   useEffect(() => {
       const calculateAllContributions = async () => {
           // Use centralized partners if available, fallback to project partners for backward compatibility
           const partnersToUse = centralizedPartners.length > 0 ? centralizedPartners : (project?.partners || []);

           if (!project?.contributions || partnersToUse.length === 0) {
               setAllContributionsByPartner([]);
               return;
           }

           try {
               const aggregatedContributions = await aggregatePartnerContributions(
                   project.contributions,
                   partnersToUse,
                   percentageOverrideData || undefined
               );
               const mappedContributions = aggregatedContributions.map(partner => ({
                   name: partner.partnerName,
                   total: partner.totalAmount,
                   percentage: partner.percentage,
                   fill: partner.fill,
                   isSourceProject: partner.partnerName.includes('('),
                   sourceProjectName: partner.partnerName.includes('(')
                       ? partner.partnerName.split('(')[1]?.replace(')', '')
                       : undefined
               }));
               setAllContributionsByPartner(mappedContributions);
           } catch (error) {
               console.error('Error calculating all partner contributions:', error);
               setAllContributionsByPartner([]);
           }
       };

       calculateAllContributions();
   }, [centralizedPartners, project?.partners, project?.contributions, percentageOverrideData]);

   const contributionChartConfig = useMemo(() => allContributionsByPartner?.reduce((acc, partner) => {
     acc[partner.name] = { label: partner.name, color: partner.fill };
     return acc;
   }, {} as ChartConfig) ?? {}, [allContributionsByPartner]);

    const expensesByCategory = useMemo(() => {
        if (!project?.expenses) return [];
        const expensesToConsider = project.expenses.filter(e => expenseFilters.accountEverything || !e.unaccounted);
        const currentTotalExpenses = expensesToConsider.reduce((sum, e) => sum + e.amount, 0);

        const groupedExpenses = expensesToConsider.reduce((acc, expense) => {
            const categoryName = expense.category || t('categoryTypeOther');
            acc[categoryName] = (acc[categoryName] || 0) + expense.amount;
            return acc;
        }, {} as Record<string, number>);

        return Object.entries(groupedExpenses).map(([name, total], index) => ({
            name,
            total,
            percentage: currentTotalExpenses > 0 ? (total / currentTotalExpenses) * 100 : 0,
            fill: chartColors[index % chartColors.length],
        })).filter(cat => cat.total > 0);
    }, [project?.expenses, t, expenseFilters.accountEverything]);


    const expenseChartConfig = useMemo(() => expensesByCategory?.reduce((acc, category) => {
        acc[category.name] = { label: category.name, color: category.fill };
        return acc;
    }, {} as ChartConfig) ?? {}, [expensesByCategory]);

   const filteredAndSortedContributions = useMemo(() => {
     if (!project?.contributions) return [];
     let filtered = project.contributions;
     if (contributionFilters.partnerId !== 'all') {
       filtered = filtered.filter(c => c.partnerId === contributionFilters.partnerId);
     }
     if (contributionFilters.startDate) {
         const start = startOfDay(contributionFilters.startDate);
         filtered = filtered.filter(c => isEqual(c.date, start) || isAfter(c.date, start));
     }
     if (contributionFilters.endDate) {
         const end = endOfDay(contributionFilters.endDate);
         filtered = filtered.filter(c => isEqual(c.date, end) || isBefore(c.date, end));
     }
     if (contributionFilters.description.trim()) {
       const searchTerm = contributionFilters.description.trim().toLowerCase();
       filtered = filtered.filter(c => c.description?.toLowerCase().includes(searchTerm));
     }
     if (contributionFilters.type !== 'all') {
       filtered = filtered.filter(c => c.type === contributionFilters.type);
     }
     const { column, direction } = contributionSorting;
     const sortedFiltered = [...filtered];
     sortedFiltered.sort((a, b) => {
       let valA: string | number | Date | undefined;
       let valB: string | number | Date | undefined;

            if (column === 'partnerName') { valA = project.partners?.find(p => p.id === a.partnerId)?.name || ''; valB = project.partners?.find(p => p.id === b.partnerId)?.name || ''; }
            else if (column === 'description') { valA = a.description || ''; valB = b.description || ''; }
            else if (column === 'type') { valA = a.type; valB = b.type; }
            else if (column === 'amount') { valA = a.amount; valB = b.amount; }
            else if (column === 'date') { valA = a.date; valB = b.date; }


        if (valA === undefined && valB === undefined) return 0;
        if (valA === undefined) return direction === 'asc' ? 1 : -1;
        if (valB === undefined) return direction === 'asc' ? -1 : 1;
       if (typeof valA === 'string' && typeof valB === 'string') {
         const comparison = valA.localeCompare(valB, language);
         return direction === 'asc' ? comparison : -comparison;
       } else if (typeof valA === 'number' && typeof valB === 'number') {
         return direction === 'asc' ? valA - valB : valB - valA;
       } else if (valA instanceof Date && valB instanceof Date) {
         const comparison = valA.getTime() - valB.getTime();
         return direction === 'asc' ? comparison : -comparison;
       }
       return 0;
     });
     return sortedFiltered;
   }, [project?.contributions, project?.partners, contributionFilters, contributionSorting, language]);

    const filteredAndSortedExpenses = useMemo(() => {
        if (!project?.expenses) return [];
        let filtered = project.expenses;
        if (expenseFilters.startDate) {
            const start = startOfDay(expenseFilters.startDate);
            filtered = filtered.filter(e => isEqual(e.date, start) || isAfter(e.date, start));
        }
        if (expenseFilters.endDate) {
            const end = endOfDay(expenseFilters.endDate);
            filtered = filtered.filter(e => isEqual(e.date, end) || isBefore(e.date, end));
        }
        if (expenseFilters.description.trim()) {
            const searchTerm = expenseFilters.description.trim().toLowerCase();
            filtered = filtered.filter(e =>
                (e.description?.toLowerCase().includes(searchTerm)) ||
                (e.payee?.toLowerCase().includes(searchTerm)) ||
                (e.checkNumber?.toLowerCase().includes(searchTerm))
            );
        }
        if (expenseFilters.invoiceStatus !== 'all') {
            const status = expenseFilters.invoiceStatus === 'with';
            filtered = filtered.filter(e => e.hasInvoice === status);
        }
        if (expenseFilters.categoryId !== 'all') {
             filtered = filtered.filter(e => e.category === expenseFilters.categoryId);
        }
        if (expenseFilters.type !== 'all') {
            filtered = filtered.filter(e => e.type === expenseFilters.type);
        }
        const { column, direction } = expenseSorting;
        const sortedFiltered = [...filtered];
        sortedFiltered.sort((a, b) => {
            let valA: string | number | Date | undefined;
            let valB: string | number | Date | undefined;

            if (column === 'payee') { valA = a.payee || ''; valB = b.payee || ''; }
            else if (column === 'category') { valA = a.category; valB = b.category; }
            else if (column === 'description') { valA = a.description || ''; valB = b.description || ''; }
            // No sort for 'invoice' as it's boolean/display only
            else if (column === 'checkNumber') { valA = a.checkNumber || ''; valB = b.checkNumber || ''; }
            else if (column === 'type') { valA = a.type; valB = b.type; }
            else if (column === 'amount') { valA = a.amount; valB = b.amount; }
            else if (column === 'date') { valA = a.date; valB = b.date; }


            if (valA === undefined && valB === undefined) return 0;
            if (valA === undefined) return direction === 'asc' ? 1 : -1;
            if (valB === undefined) return direction === 'asc' ? -1 : 1;
            if (typeof valA === 'string' && typeof valB === 'string') {
                const comparison = valA.localeCompare(valB, language);
                return direction === 'asc' ? comparison : -comparison;
            } else if (typeof valA === 'number' && typeof valB === 'number') {
                return direction === 'asc' ? valA - valB : valB - valA;
            } else if (valA instanceof Date && valB instanceof Date) {
                const comparison = valA.getTime() - valB.getTime();
                return direction === 'asc' ? comparison : -comparison;
            }
            return 0;
        });
        return sortedFiltered;
    }, [project?.expenses, expenseFilters, expenseSorting, language]);

    const filteredAndSortedApartments = useMemo(() => {
        if (!project?.apartments) return [];
        let filtered = project.apartments;
        if (apartmentFilters.floor !== 'all') {
            const floorNum = parseInt(apartmentFilters.floor, 10);
            if (!isNaN(floorNum)) {
                filtered = filtered.filter(apt => apt.floor === floorNum);
            }
        }
        if (apartmentFilters.status !== 'all') {
            filtered = filtered.filter(apt => apt.status === apartmentFilters.status);
        }
        const { column, direction } = apartmentSorting;
        const sortedFiltered = [...filtered];
        sortedFiltered.sort((a, b) => {
             let valA: string | number | undefined;
             let valB: string | number | undefined;
            if (column === 'number') {
                valA = a.number;
                valB = b.number;
                const comparison = String(valA).localeCompare(String(valB), language, { numeric: true, sensitivity: 'base' });
                return direction === 'asc' ? comparison : -comparison;
            } else if (column === 'type') {
                // Sort by type first, then by number as secondary sort
                valA = a.type;
                valB = b.type;
                const typeComparison = String(valA).localeCompare(String(valB), language);
                if (typeComparison !== 0) {
                    return direction === 'asc' ? typeComparison : -typeComparison;
                }
                // If types are equal, sort by number
                const numberComparison = String(a.number).localeCompare(String(b.number), language, { numeric: true, sensitivity: 'base' });
                return direction === 'asc' ? numberComparison : -numberComparison;
            } else {
                 valA = a[column];
                 valB = b[column];
            }
            if (valA === undefined && valB === undefined) return 0;
            if (valA === undefined) return direction === 'asc' ? 1 : -1;
            if (valB === undefined) return direction === 'asc' ? -1 : 1;
            if (typeof valA === 'string' && typeof valB === 'string') {
                const comparison = valA.localeCompare(valB, language);
                return direction === 'asc' ? comparison : -comparison;
            } else if (typeof valA === 'number' && typeof valB === 'number') {
                return direction === 'asc' ? valA - valB : valB - valA;
            }
            return 0;
        });
        return sortedFiltered;
    }, [project?.apartments, apartmentFilters, apartmentSorting, language]);

    const filteredAndSortedSales = useMemo(() => {
        if (!project?.sales) return [];
        let filtered = project.sales;
        if (saleFilters.startDate) {
            const start = startOfDay(saleFilters.startDate);
            filtered = filtered.filter(s => isEqual(s.date, start) || isAfter(s.date, start));
        }
        if (saleFilters.endDate) {
            const end = endOfDay(saleFilters.endDate);
            filtered = filtered.filter(s => isEqual(s.date, end) || isBefore(s.date, end));
        }
        if (saleFilters.clientName.trim()) {
            const searchTerm = saleFilters.clientName.trim().toLowerCase();
            filtered = filtered.filter(s => s.clientName?.toLowerCase().includes(searchTerm));
        }
        const { column, direction } = saleSorting;
        const sortedFiltered = [...filtered];
        sortedFiltered.sort((a, b) => {
            let valA: string | number | Date | undefined;
            let valB: string | number | Date | undefined;
            if (column === 'apartmentNumber') {
                valA = project.apartments?.find(apt => apt.id === a.apartmentId)?.number || '';
                valB = project.apartments?.find(apt => apt.id === b.apartmentId)?.number || '';
            } else if (column === 'date' || column === 'amountDeclared' || column === 'amountUndeclared' || column === 'totalAmount' || column === 'clientName') {
                valA = a[column];
                valB = b[column];
            }
            if (valA === undefined && valB === undefined) return 0;
            if (valA === undefined) return direction === 'asc' ? 1 : -1;
            if (valB === undefined) return direction === 'asc' ? -1 : 1;
            if (typeof valA === 'string' && typeof valB === 'string') {
                 if (column === 'apartmentNumber') {
                     const comparison = String(valA).localeCompare(String(valB), language, { numeric: true, sensitivity: 'base' });
                     return direction === 'asc' ? comparison : -comparison;
                 }
                const comparison = valA.localeCompare(valB, language);
                return direction === 'asc' ? comparison : -comparison;
            } else if (typeof valA === 'number' && typeof valB === 'number') {
                return direction === 'asc' ? valA - valB : valB - valA;
            } else if (valA instanceof Date && valB instanceof Date) {
                const comparison = valA.getTime() - valB.getTime();
                return direction === 'asc' ? comparison : -comparison;
            }
            return 0;
        });
        return sortedFiltered;
    }, [project?.sales, project?.apartments, saleFilters, saleSorting, language]);

    const availableFloors = useMemo(() => {
      if (!project || project.levels < 0) return [];
      return Array.from({ length: project.levels + 1 }, (_, i) => i);
    }, [project?.levels]);

    const availableApartments = useMemo(() => {
        return project?.apartments?.filter(apt => apt.status === 'available') ?? [];
    }, [project?.apartments]);

    const allApartments = useMemo(() => {
        return project?.apartments ?? [];
    }, [project?.apartments]);




   const isLoading = isAuthLoading || isProjectLoading || isLoadingCategories;

   const renderSortIcon = (column: SortableContributionColumn | SortableExpenseColumn | SortableApartmentColumn | SortableSaleColumn, tableType: 'contribution' | 'expense' | 'apartment' | 'sale') => {
      let sortingState;
      switch (tableType) {
        case 'contribution': sortingState = contributionSorting; break;
        case 'expense': sortingState = expenseSorting; break;
        case 'apartment': sortingState = apartmentSorting; break;
        case 'sale': sortingState = saleSorting; break;
        default: return null;
      }
      if (sortingState.column !== column) return <ArrowUpDown className="ml-2 h-4 w-4 opacity-50 no-print-in-section" />;
      return sortingState.direction === 'asc' ? <ArrowUp className="ml-2 h-4 w-4 text-primary no-print-in-section" /> : <ArrowDown className="ml-2 h-4 w-4 text-primary no-print-in-section" />;
   };


  if (isLoading) { return <ProjectDetailSkeleton />; }

   if (projectError) {
     return (
       <div className="container mx-auto px-3 py-6 text-center text-destructive flex flex-col items-center">
         <AlertCircle className="w-12 h-12 mb-4" />
         <h1 className="text-2xl font-semibold ">{t('errorTitle')}</h1>
         <p className="text-muted-foreground mt-2">{projectError.message || t('errorMessage')}</p>
         <Button onClick={() => refetchProject()} className="mt-4">
           {t('retry')}
         </Button>
         <Button asChild variant="link" className="mt-2">
           <Link href="/">
             <ArrowLeft className="mr-2 h-4 w-4" /> {t('backToProjects')}
           </Link>
         </Button>
       </div>
     );
   };

  if (!project) {
    return (
      <div className="container mx-auto px-3 py-6 text-center">
        <h1 className="text-2xl font-semibold text-destructive">{t('projectNotFoundTitle')}</h1>
        <p className="text-muted-foreground mt-2">{t('projectNotFoundDescription')}</p>
        <Button asChild variant="link" className="mt-4">
          <Link href="/">
            <ArrowLeft className="mr-2 h-4 w-4" /> {t('backToProjects')}
          </Link>
        </Button>
      </div>
    );
  };

  const CustomTooltipContent = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background/80 p-2 border rounded-md shadow-lg text-sm">
          <p className="font-semibold">{data.name}: {data.total.toLocaleString(language, { style: 'currency', currency: currency })} ({data.percentage.toFixed(2)}%)</p>
        </div>
      );
    }
    return null;
  };


  return (
    <div className="container mx-auto px-3 py-6 print-container">
      <Card className="mb-3 project-details-card no-print">
         <CardHeader className="pb-3">
             <div className="flex flex-col lg:flex-row lg:items-center gap-3">
                 {/* Project Title and Balance */}
                 <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3 flex-shrink-0">
                     <CardTitle className="text-xl lg:text-2xl font-bold text-primary flex items-center gap-2">
                        <Building className="h-5 w-5 lg:h-6 lg:w-6" /> {project.name}
                     </CardTitle>
                     <Badge variant={balance >= 0 ? 'default' : 'destructive'} className={cn('whitespace-nowrap font-semibold text-xs lg:text-sm', balance >= 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200')}>
                        {t('balance')}: {balance.toLocaleString(language, { style: 'currency', currency: currency })}
                     </Badge>
                 </div>

                 {/* Project Details - Horizontal Layout */}
                 <div className="flex-grow">
                     {/* Location */}
                     <div className="flex items-center text-muted-foreground text-sm mb-2 lg:mb-1">
                         <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                         <span className="truncate">{project.city} - {project.address}</span>
                     </div>

                     {/* Compact Stats Grid */}
                     <div className="project-stats-grid grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-4 gap-2 lg:gap-4 text-xs lg:text-sm">
                         <div className="flex items-center gap-1 min-w-0">
                             <Maximize className="h-3 w-3 lg:h-4 lg:w-4 text-primary/80 flex-shrink-0" />
                             <span className="truncate-text">{t('area')}: {project.area} m²</span>
                         </div>
                         <div className="flex items-center gap-1 min-w-0">
                             <Layers className="h-3 w-3 lg:h-4 lg:w-4 text-primary/80 flex-shrink-0" />
                             <span className="truncate-text">{t('levels')}: R+{project.levels}</span>
                         </div>
                         <div className="flex items-center gap-1 min-w-0">
                             <Users className="h-3 w-3 lg:h-4 lg:w-4 text-primary/80 flex-shrink-0" />
                             <span className="truncate-text">{t('partnersCount')}: {project.partners?.length ?? 0}</span>
                         </div>
                         <div className="flex items-center gap-1 min-w-0" title={t('salesAmountTotal')}>
                             <Banknote className="h-3 w-3 lg:h-4 lg:w-4 text-primary/80 flex-shrink-0" />
                             <span className="truncate-text">
                                 {t('sales')}: {totalSalesAmount.toLocaleString(language, { style: 'currency', currency: currency })} ({salesCount})
                             </span>
                         </div>
                     </div>
                 </div>
             </div>
         </CardHeader>
      </Card>

       <Tabs defaultValue="expenses" value={activeTab} onValueChange={setActiveTab} className="tabs-list">
          <TabsList className={cn("grid w-full mb-3 no-print overflow-x-auto md:overflow-x-visible", "grid-cols-2 sm:grid-cols-3 md:grid-cols-7")}>
             <TabsTrigger value="expenses" className="tab-trigger-responsive">
                 <TrendingDown className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                 <span className="hidden sm:inline">{t('expenses')}</span>
                 <span className="sm:hidden">{getTabName('expenses', true)}</span>
             </TabsTrigger>
             <TabsTrigger value="contributions" className="tab-trigger-responsive">
                 <TrendingUp className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                 <span className="hidden sm:inline">{t('contributions')}</span>
                 <span className="sm:hidden">{getTabName('contributions', true)}</span>
             </TabsTrigger>
             <TabsTrigger value="apartments" className="tab-trigger-responsive">
                 <HomeIcon className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                 <span className="hidden sm:inline">{t('apartments')}</span>
                 <span className="sm:hidden">{getTabName('apartments', true)}</span>
             </TabsTrigger>
             <TabsTrigger value="sales" className="tab-trigger-responsive">
                 <Banknote className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                 <span className="hidden sm:inline">{t('sales')}</span>
                 <span className="sm:hidden">{getTabName('sales', true)}</span>
             </TabsTrigger>
             <TabsTrigger value="partners" className="tab-trigger-responsive">
                 <Users className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                 <span className="hidden sm:inline">{t('contributors') || t('partners')}</span>
                 <span className="sm:hidden">{getTabName('contributors', true) || getTabName('partners', true)}</span>
             </TabsTrigger>
             <TabsTrigger value="payments" className="tab-trigger-responsive">
                 <PieChartIcon className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                 <span className="hidden sm:inline">{t('paymentTracking')}</span>
                 <span className="sm:hidden">{getTabName('paymentTracking', true)}</span>
             </TabsTrigger>
             <TabsTrigger value="adminDocs" className="tab-trigger-responsive">
                 <FileTextIcon className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                 <span className="hidden sm:inline">{t('adminDocuments')}</span>
                 <span className="sm:hidden">{t('adminDocumentsShort')}</span>
             </TabsTrigger>
          </TabsList>

           <TabsContent value="contributions" className="tabs-content print-section">
             <Card className="print-section">
                <CardHeader className="tab-card-header flex flex-col sm:flex-row items-center justify-end gap-2 no-print">
                   <div className="flex gap-2 mt-2 sm:mt-0 flex-wrap sm:flex-nowrap justify-center sm:justify-end w-full sm:w-auto print-button-container no-print">
                        <Button variant="outline" size="sm" onClick={() => window.print()} disabled={!project} id="print-contributions-button">
                            <Printer className="mr-2 h-4 w-4" />
                            {t('printButton')}
                        </Button>
                        {isEditor && (
                            <Button variant="outline" size="sm" onClick={() => setIsAddContributionOpen(true)} disabled={addContributionMutation.isPending || updateContributionMutation.isPending} id="add-contribution-button">
                                <PlusCircle className="mr-2 h-4 w-4" /> {t('addContribution')}
                            </Button>
                        )}
                   </div>
                    <AddContributionDialog
                        partners={project.partners || []}
                        onAddContribution={handleAddContribution}
                        open={isAddContributionOpen}
                        onOpenChange={setIsAddContributionOpen}
                        projectId={projectId}
                    />
                    <AddContributionDialog
                         partners={project.partners || []}
                         onEditContribution={handleEditContribution}
                         initialData={editingContribution}
                         open={isEditContributionOpen}
                         onOpenChange={setIsEditContributionOpen}
                         projectId={projectId}
                     />
                    <DeleteContributionDialog
                        contribution={contributionToDelete}
                        partners={project.partners || []}
                        open={isDeleteContributionOpen}
                        onOpenChange={setIsDeleteContributionOpen}
                        onConfirmDelete={handleDeleteContribution}
                        isDeleting={deleteContributionMutation.isPending}
                    />
                </CardHeader>
                <CardContent>
                    <div className="filters-section flex flex-wrap items-end gap-3 mb-4 p-3 border rounded-lg bg-muted/50 no-print">
                        <div className="flex-grow min-w-[150px] space-y-1">
                            <Label htmlFor="filter-partner" className={cn("block", language === 'ar' ? "sm:text-right" : "sm:text-left", isMobile && "text-center")}>{t('filterByPartner')}</Label>
                            <Select value={contributionFilters.partnerId} onValueChange={(value) => handleContributionFilterChange('partnerId', value)}>
                                <SelectTrigger id="filter-partner"><SelectValue placeholder={t('selectPartnerPlaceholder')} /></SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">{t('allPartners')}</SelectItem>
                                    {project.partners?.map(partner => (<SelectItem key={partner.id} value={partner.id}>{partner.name}</SelectItem>))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex-grow min-w-[150px] space-y-1">
                            <Label htmlFor="filter-payment-type" className={cn("block", language === 'ar' ? "sm:text-right" : "sm:text-left", isMobile && "text-center")}>{t('filterByPaymentType')}</Label>
                            <Select value={contributionFilters.type} onValueChange={(value) => handleContributionFilterChange('type', value)}>
                                <SelectTrigger id="filter-payment-type"><SelectValue placeholder={t('selectPaymentTypePlaceholder')} /></SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">{t('allPaymentTypes')}</SelectItem>
                                    <SelectItem value="Espèce">{t('cash')}</SelectItem>
                                    <SelectItem value="Virement">{t('bankTransfer')}</SelectItem>
                                    <SelectItem value="Chèque">{t('check')}</SelectItem>
                                    <SelectItem value="Autre">{t('other')}</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex-grow min-w-[150px] space-y-1">
                             <Label htmlFor="filter-start-date" className={cn("block", language === 'ar' ? "sm:text-right" : "sm:text-left", isMobile && "text-center")}>{t('filterByStartDate')}</Label>
                             <div className="flex items-center gap-2">
                                <Input
                                    id="filter-start-date"
                                    type="text"
                                    value={contributionDateStrings.startDate}
                                    onChange={(e) => handleContributionDateInputChange('startDate', e.target.value)}
                                    placeholder={DATE_FORMAT.toUpperCase()}
                                    className="flex-1"
                                />
                                <Popover>
                                    <PopoverTrigger asChild>
                                      <Button variant={"outline"} size="icon" className="w-10 h-10">
                                        <CalendarIcon className="h-4 w-4" />
                                        <span className="sr-only">{t('pickDatePlaceholder')}</span>
                                      </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={contributionFilters.startDate ?? undefined} onSelect={(date) => handleContributionDateSelect('startDate', date)} initialFocus locale={dateLocale}/></PopoverContent>
                                </Popover>
                             </div>
                        </div>
                        <div className="flex-grow min-w-[150px] space-y-1">
                            <Label htmlFor="filter-end-date" className={cn("block", language === 'ar' ? "sm:text-right" : "sm:text-left", isMobile && "text-center")}>{t('filterByEndDate')}</Label>
                            <div className="flex items-center gap-2">
                                <Input
                                    id="filter-end-date"
                                    type="text"
                                    value={contributionDateStrings.endDate}
                                    onChange={(e) => handleContributionDateInputChange('endDate', e.target.value)}
                                    placeholder={DATE_FORMAT.toUpperCase()}
                                    className="flex-1"
                                />
                                <Popover>
                                    <PopoverTrigger asChild>
                                      <Button variant={"outline"} size="icon" className="w-10 h-10">
                                        <CalendarIcon className="h-4 w-4" />
                                        <span className="sr-only">{t('pickDatePlaceholder')}</span>
                                      </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={contributionFilters.endDate ?? undefined} onSelect={(date) => handleContributionDateSelect('endDate', date)} initialFocus locale={dateLocale} disabled={(date) => contributionFilters.startDate ? isBefore(date, contributionFilters.startDate) : false}/></PopoverContent>
                                </Popover>
                            </div>
                        </div>
                        <div className="flex-grow min-w-[200px] space-y-1">
                            <Label htmlFor="filter-description" className={cn("block", language === 'ar' ? "sm:text-right" : "sm:text-left", isMobile && "text-center")}>{t('filterByDescription')}</Label>
                            <Input id="filter-description" type="text" placeholder={t('searchInDescription')} value={contributionFilters.description} onChange={(e) => handleContributionFilterChange('description', e.target.value)}/>
                        </div>
                        <Button variant="ghost" size="icon" onClick={resetContributionFilters} title={t('resetFilters')} className="self-end h-8 w-8 no-print">
                            <XCircle className="h-5 w-5 text-muted-foreground" /><span className="sr-only">{t('resetFilters')}</span>
                        </Button>
                    </div>
                    <div className="print-table-container contributions-table">
                      {filteredAndSortedContributions.length > 0 ? (
                        <Table>
                           <TableHeader className="screen-only-table-header">
                           <TableRow>
                                {language === 'ar' ? (
                                    <>
                                        {isEditor && <TableHead className="text-left w-[60px] table-action-col no-print-in-section">{t('actions')}</TableHead>}
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('partnerName')}><div className="flex items-center justify-end gap-1">{t('source')} <span className="sort-icon no-print-in-section">{renderSortIcon('partnerName', 'contribution')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('description')}><div className="flex items-center justify-end gap-1">{t('descriptionLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('description', 'contribution')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('type')}><div className="flex items-center justify-end gap-1">{t('type')} <span className="sort-icon no-print-in-section">{renderSortIcon('type', 'contribution')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('amount')}><div className="flex items-center justify-end gap-1">{t('amount')} <span className="sort-icon no-print-in-section">{renderSortIcon('amount', 'contribution')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('date')}><div className="flex items-center justify-end gap-1">{t('dateLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('date', 'contribution')}</span></div></TableHead>
                                    </>
                                ) : (
                                    <>
                                        {isEditor && <TableHead className="text-left w-[60px] table-action-col no-print-in-section">{t('actions')}</TableHead>}
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('partnerName')}><div className="flex items-center gap-1">{t('source')} <span className="sort-icon no-print-in-section">{renderSortIcon('partnerName', 'contribution')}</span></div></TableHead>
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('description')}><div className="flex items-center gap-1">{t('descriptionLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('description', 'contribution')}</span></div></TableHead>
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('type')}><div className="flex items-center gap-1">{t('type')} <span className="sort-icon no-print-in-section">{renderSortIcon('type', 'contribution')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('amount')}><div className="flex items-center justify-end gap-1">{t('amount')} <span className="sort-icon no-print-in-section">{renderSortIcon('amount', 'contribution')}</span></div></TableHead>
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleContributionSort('date')}><div className="flex items-center gap-1">{t('dateLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('date', 'contribution')}</span></div></TableHead>
                                    </>
                                )}
                            </TableRow>
                           </TableHeader>
                          <TableBody>
                            {filteredAndSortedContributions.map((contribution, index) => (
                              <TableRow key={contribution.id} className={cn(index % 2 === 0 ? 'bg-muted/50' : '', "font-medium")}>
                                {language === 'ar' ? (
                                    <>
                                        {isEditor && (
                                            <TableCell className="text-left py-1.5 table-action-col no-print-in-section">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-8 w-8"
                                                            disabled={addContributionMutation.isPending || updateContributionMutation.isPending || deleteContributionMutation.isPending}
                                                        >
                                                            <MoreVertical className="h-4 w-4" />
                                                            <span className="sr-only">{t('actions')}</span>
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem
                                                            onClick={() => openEditContributionDialog(contribution)}
                                                            disabled={addContributionMutation.isPending || updateContributionMutation.isPending || deleteContributionMutation.isPending}
                                                        >
                                                            <Pencil className="mr-2 h-4 w-4" />
                                                            {t('edit')}
                                                        </DropdownMenuItem>
                                                        {canDeleteContributions && (
                                                            <>
                                                                <DropdownMenuSeparator />
                                                                <DropdownMenuItem
                                                                    onClick={() => openDeleteContributionDialog(contribution)}
                                                                    disabled={addContributionMutation.isPending || updateContributionMutation.isPending || deleteContributionMutation.isPending}
                                                                    className="text-destructive focus:text-destructive"
                                                                >
                                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                                    {t('delete')}
                                                                </DropdownMenuItem>
                                                            </>
                                                        )}
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        )}
                                        <TableCell className="text-right font-medium py-1.5">
                                            <div className="flex items-center justify-end gap-2">
                                                {isProjectSourcedContribution(contribution) && (
                                                    <Building className="h-3 w-3 text-muted-foreground" />
                                                )}
                                                {getContributionSourceDisplayName(contribution, project.partners || [], centralizedPartners)}
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-right font-medium py-1.5">{contribution.description || '-'}</TableCell>
                                        <TableCell className="text-right font-medium py-1.5">{getTranslatedType(contribution.type)}</TableCell>
                                        <TableCell className="text-right font-medium py-1.5 amount-column">{contribution.amount.toLocaleString(language, { style: 'currency', currency: currency })}</TableCell>
                                        <TableCell className="text-right font-medium py-1.5 print:text-xs date-column">
                                            <span className="print:hidden">{format(new Date(contribution.date), DATE_FORMAT)}</span>
                                            <span className="hidden print:inline">{format(new Date(contribution.date), PRINT_DATE_FORMAT)}</span>
                                        </TableCell>
                                    </>
                                ) : (
                                    <>
                                        {isEditor && (
                                            <TableCell className="text-left py-1.5 table-action-col no-print-in-section">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-8 w-8"
                                                            disabled={addContributionMutation.isPending || updateContributionMutation.isPending || deleteContributionMutation.isPending}
                                                        >
                                                            <MoreVertical className="h-4 w-4" />
                                                            <span className="sr-only">{t('actions')}</span>
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem
                                                            onClick={() => openEditContributionDialog(contribution)}
                                                            disabled={addContributionMutation.isPending || updateContributionMutation.isPending || deleteContributionMutation.isPending}
                                                        >
                                                            <Pencil className="mr-2 h-4 w-4" />
                                                            {t('edit')}
                                                        </DropdownMenuItem>
                                                        {canDeleteContributions && (
                                                            <>
                                                                <DropdownMenuSeparator />
                                                                <DropdownMenuItem
                                                                    onClick={() => openDeleteContributionDialog(contribution)}
                                                                    disabled={addContributionMutation.isPending || updateContributionMutation.isPending || deleteContributionMutation.isPending}
                                                                    className="text-destructive focus:text-destructive"
                                                                >
                                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                                    {t('delete')}
                                                                </DropdownMenuItem>
                                                            </>
                                                        )}
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        )}
                                        <TableCell className="text-left font-medium py-1.5">
                                            <div className="flex items-center gap-2">
                                                {isProjectSourcedContribution(contribution) && (
                                                    <Building className="h-3 w-3 text-muted-foreground" />
                                                )}
                                                {getContributionSourceDisplayName(contribution, project.partners || [], centralizedPartners)}
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-left font-medium py-1.5">{contribution.description || '-'}</TableCell>
                                        <TableCell className="text-left font-medium py-1.5">{getTranslatedType(contribution.type)}</TableCell>
                                        <TableCell className="text-right font-medium py-1.5 amount-column">{contribution.amount.toLocaleString(language, { style: 'currency', currency: currency })}</TableCell>
                                        <TableCell className="text-left font-medium py-1.5 print:text-xs date-column">
                                            <span className="print:hidden">{format(new Date(contribution.date), DATE_FORMAT)}</span>
                                            <span className="hidden print:inline">{format(new Date(contribution.date), PRINT_DATE_FORMAT)}</span>
                                        </TableCell>
                                    </>
                                )}
                              </TableRow>
                            ))}
                             <TableRow className="font-bold bg-secondary hover:bg-secondary">
                                {language === 'ar' ? (
                                    <>
                                        {isEditor && <TableCell className="py-1.5"></TableCell>}
                                        <TableCell colSpan={3} className={cn("py-1.5 text-left")} > {t('totalFilteredContributions')} ({filteredAndSortedContributions.length}) </TableCell>
                                        <TableCell className="text-right py-1.5 amount-column">{filteredAndSortedContributions.reduce((sum, c) => sum + c.amount, 0).toLocaleString(language, { style: 'currency', currency: currency })}</TableCell>
                                        <TableCell className="py-1.5"></TableCell>
                                    </>
                                ) : (
                                    <>
                                        {isEditor && <TableCell className="py-1.5"></TableCell>}
                                        <TableCell colSpan={3} className={cn("py-1.5 text-right")} > {t('totalFilteredContributions')} ({filteredAndSortedContributions.length}) </TableCell>
                                        <TableCell className="text-right py-1.5 amount-column"> {filteredAndSortedContributions.reduce((sum, c) => sum + c.amount, 0).toLocaleString(language, { style: 'currency', currency: currency })} </TableCell>
                                        <TableCell className="py-1.5"></TableCell>
                                    </>
                                )}
                            </TableRow>
                          </TableBody>
                        </Table>
                      ) : ( <p className="text-muted-foreground text-center py-4 no-print">{t('noContributionsMatchFilters')}</p> )}
                    </div>
                    {/* Contribution Visualization Section (Web only) */}
                    {allContributionsByPartner && allContributionsByPartner.length > 0 && (
                        <div className="mt-6 no-print grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Partner Summary Table */}
                            <PartnerSummaryTable
                                contributions={project.contributions || []}
                                partners={centralizedPartners.length > 0 ? centralizedPartners : (project.partners || [])}
                                percentageOverride={percentageOverrideData || undefined}
                            />

                            {/* Pie Chart */}
                            <Card>
                                {percentageOverrideData?.enabled && (
                                    <CardHeader className="pb-3">
                                        <CardTitle className={cn(
                                            "text-sm flex items-center gap-2",
                                            language === 'ar' && "flex-row-reverse text-right"
                                        )}>
                                            <PieChartIcon className="h-4 w-4" />
                                            {t('contributions')}
                                            <Badge variant="outline" className="text-xs text-blue-600 border-blue-600">
                                                {t('manual')}
                                            </Badge>
                                        </CardTitle>
                                    </CardHeader>
                                )}
                                <CardContent className={percentageOverrideData?.enabled ? "pt-0" : "pt-6"}>
                                    <ChartContainer config={contributionChartConfig} className="mx-auto aspect-square max-h-[300px] sm:max-h-[350px]">
                                        <PieChart>
                                            <ChartTooltip content={<CustomTooltipContent />} />
                                            <Pie
                                                data={allContributionsByPartner}
                                                dataKey="total"
                                                nameKey="name"
                                                label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }) => {
                                                    const RADIAN = Math.PI / 180;
                                                    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
                                                    const x = cx + radius * Math.cos(-midAngle * RADIAN);
                                                    const y = cy + radius * Math.sin(-midAngle * RADIAN);
                                                    if (percent * 100 < 5) return null; // Don't label small slices
                                                    return (
                                                        <text
                                                            x={x}
                                                            y={y}
                                                            fill="white"
                                                            textAnchor={x > cx ? 'start' : 'end'}
                                                            dominantBaseline="central"
                                                            className="text-xs font-semibold"
                                                        >
                                                            {`${name} (${(percent * 100).toFixed(0)}%)`}
                                                        </text>
                                                    );
                                                }}
                                                labelLine={false}
                                            >
                                                {allContributionsByPartner.map((entry, index) => (
                                                    <Cell key={`cell-${index}`} fill={entry.fill} />
                                                ))}
                                            </Pie>
                                            <ChartLegend content={<ChartLegendContent nameKey="name" maxItemsPerLine={4} />} />
                                        </PieChart>
                                    </ChartContainer>
                                </CardContent>
                            </Card>
                        </div>
                    )}
                </CardContent>
             </Card>
          </TabsContent>

          <TabsContent value="expenses" className="tabs-content print-section print-expenses">
           <TooltipProvider delayDuration={100}>
            <Card>
              <CardHeader className="tab-card-header flex flex-col sm:flex-row items-center justify-end gap-2 no-print pb-3">
                  <div className="flex gap-2 mt-2 sm:mt-0 flex-wrap sm:flex-nowrap justify-center sm:justify-end w-full sm:w-auto print-button-container no-print">
                    <Button variant="outline" size="sm" onClick={() => setIsMissingDocsDialogOpen(true)} id="missing-docs-button">
                        <AlertCircle className="mr-2 h-4 w-4" /> {t('missingDocumentsButton')}
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => window.print()} id="print-expenses-button">
                        <Printer className="mr-2 h-4 w-4" /> {t('printButton')}
                    </Button>
                   {isEditor && (
                       <Button variant="outline" size="sm" onClick={() => setIsAddExpenseOpen(true)} disabled={addExpenseMutation.isPending || updateExpenseMutation.isPending} id="add-expense-button">
                          <PlusCircle className="mr-2 h-4 w-4" /> {t('addExpense')}
                      </Button>
                   )}
                  </div>
                   <AddExpenseDialog onAddExpense={handleAddExpense} open={isAddExpenseOpen} onOpenChange={setIsAddExpenseOpen} projectId={projectId} categories={categories} onAddCategory={handleAddCategory} interventions={project?.interventions || []}/>
                   <AddExpenseDialog onEditExpense={handleEditExpense} initialData={editingExpense} open={isEditExpenseOpen} onOpenChange={setIsEditExpenseOpen} projectId={projectId} categories={categories} onAddCategory={handleAddCategory} interventions={project?.interventions || []}/>
              </CardHeader>
              <CardContent>
                 <div className="filters-section w-full overflow-x-auto mb-4 p-3 border rounded-lg bg-muted/50 no-print">
                    {/* First row - Date filters */}
                    <div className="flex flex-wrap items-end gap-x-3 gap-y-3 mb-3">
                        <div className="flex-grow min-w-[140px] space-y-1">
                            <Label htmlFor="filter-expense-start-date" className={cn("block text-xs font-medium", language === 'ar' ? "text-right" : "text-left")}>{t('filterByStartDate')}</Label>
                            <div className="flex items-center gap-2">
                                <Input
                                    id="filter-expense-start-date"
                                    type="text"
                                    value={expenseDateStrings.startDate}
                                    onChange={(e) => handleExpenseDateInputChange('startDate', e.target.value)}
                                    placeholder={DATE_FORMAT.toUpperCase()}
                                    className="flex-1 h-9"
                                />
                                <Popover>
                                    <PopoverTrigger asChild>
                                      <Button variant={"outline"} size="icon" className="w-9 h-9">
                                        <CalendarIcon className="h-4 w-4" />
                                        <span className="sr-only">{t('pickDatePlaceholder')}</span>
                                      </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={expenseFilters.startDate ?? undefined} onSelect={(date) => handleExpenseDateSelect('startDate', date)} initialFocus locale={dateLocale}/></PopoverContent>
                                </Popover>
                            </div>
                        </div>
                        <div className="flex-grow min-w-[140px] space-y-1">
                            <Label htmlFor="filter-expense-end-date" className={cn("block text-xs font-medium", language === 'ar' ? "text-right" : "text-left")}>{t('filterByEndDate')}</Label>
                            <div className="flex items-center gap-2">
                                <Input
                                    id="filter-expense-end-date"
                                    type="text"
                                    value={expenseDateStrings.endDate}
                                    onChange={(e) => handleExpenseDateInputChange('endDate', e.target.value)}
                                    placeholder={DATE_FORMAT.toUpperCase()}
                                    className="flex-1 h-9"
                                />
                                <Popover>
                                    <PopoverTrigger asChild>
                                      <Button variant={"outline"} size="icon" className="w-9 h-9">
                                        <CalendarIcon className="h-4 w-4" />
                                        <span className="sr-only">{t('pickDatePlaceholder')}</span>
                                      </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={expenseFilters.endDate ?? undefined} onSelect={(date) => handleExpenseDateSelect('endDate', date)} initialFocus locale={dateLocale} disabled={(date) => expenseFilters.startDate ? isBefore(date, expenseFilters.startDate) : false}/></PopoverContent>
                                </Popover>
                            </div>
                        </div>
                        <div className="flex-grow min-w-[130px] space-y-1">
                            <Label htmlFor="filter-expense-type" className={cn("block text-xs font-medium", language === 'ar' ? "text-right" : "text-left")}>{t('filterByPaymentType')}</Label>
                            <Select value={expenseFilters.type} onValueChange={(value) => handleExpenseFilterChange('type', value as PaymentTypeFilter)}>
                                <SelectTrigger id="filter-expense-type" className="h-9"><SelectValue placeholder={t('selectTypePlaceholder')} /></SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">{t('allPaymentTypes')}</SelectItem>
                                    <SelectItem value="Espèce">{t('contributionTypeCash')}</SelectItem>
                                    <SelectItem value="Virement">{t('contributionTypeTransfer')}</SelectItem>
                                    <SelectItem value="Chèque">{t('contributionTypeCheck')}</SelectItem>
                                    <SelectItem value="Autre">{t('contributionTypeOther')}</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex-grow min-w-[130px] space-y-1">
                            <Label htmlFor="filter-category" className={cn("block text-xs font-medium", language === 'ar' ? "text-right" : "text-left")}>{t('filterByCategory')}</Label>
                            <Select value={expenseFilters.categoryId} onValueChange={(value) => handleExpenseFilterChange('categoryId', value)}>
                                <SelectTrigger id="filter-category" className="h-9"><SelectValue placeholder={isLoadingCategories ? t('loading') + '...' : t('selectCategoryPlaceholder')} /></SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">{t('allCategories')}</SelectItem>
                                    {!isLoadingCategories && categories.map(cat => (<SelectItem key={cat.id} value={cat.name}>{cat.name}</SelectItem>))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex-grow min-w-[180px] space-y-1">
                            <Label htmlFor="filter-expense-description" className={cn("block text-xs font-medium", language === 'ar' ? "text-right" : "text-left")}>{t('filterByDescriptionOrPayee')}</Label>
                            <Input id="filter-expense-description" type="text" placeholder={t('searchInDescriptionOrPayee')} value={expenseFilters.description} onChange={(e) => handleExpenseFilterChange('description', e.target.value)} className="h-9"/>
                        </div>
                    </div>

                    {/* Second row - Invoice status, checkbox, and reset button */}
                    <div className="flex flex-wrap items-center gap-x-4 gap-y-2">
                         <div className="flex-grow min-w-[200px]">
                             <Label className={cn("block text-xs font-medium mb-2", language === 'ar' ? "text-right" : "text-left")}>{t('filterByInvoiceStatus')}</Label>
                              <RadioGroup value={expenseFilters.invoiceStatus} onValueChange={(value) => handleExpenseFilterChange('invoiceStatus', value as InvoiceStatusFilter)} className={cn("flex gap-4", language === 'ar' ? "flex-row-reverse" : "flex-row")}>
                                 <div className="flex items-center gap-2"><RadioGroupItem value="all" id="invoice-all" /><Label htmlFor="invoice-all" className="text-sm">{t('invoiceStatusAll')}</Label></div>
                                 <div className="flex items-center gap-2"><RadioGroupItem value="with" id="invoice-with" /><Label htmlFor="invoice-with" className="text-sm">{t('invoiceWith')}</Label></div>
                                 <div className="flex items-center gap-2"><RadioGroupItem value="without" id="invoice-without" /><Label htmlFor="invoice-without" className="text-sm">{t('invoiceWithout')}</Label></div>
                               </RadioGroup>
                         </div>
                        <div className="flex items-center gap-2">
                            <Checkbox
                                id="account-everything"
                                checked={expenseFilters.accountEverything}
                                onCheckedChange={(checked) => handleExpenseFilterChange('accountEverything', checked as boolean)}
                            />
                            <Label htmlFor="account-everything" className="cursor-pointer whitespace-nowrap text-sm">{t('accountEverything')}</Label>
                        </div>
                        <Button variant="ghost" size="icon" onClick={resetExpenseFilters} title={t('resetFilters')} className="h-8 w-8 no-print"><XCircle className="h-5 w-5 text-muted-foreground" /><span className="sr-only">{t('resetFilters')}</span></Button>
                    </div>
                </div>
                 <div className="print-table-container expenses-table">
                    {filteredAndSortedExpenses.length > 0 ? (
                    <Table>
                        <TableHeader className="screen-only-table-header">
                             <TableRow>
                                {language === 'ar' ? (
                                    <>
                                        {isEditor && <TableHead className="text-left w-[80px] table-action-col no-print-in-section">{t('actions')}</TableHead>}
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('payee')}><div className="flex items-center justify-end gap-1">{t('payeeLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('payee', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('category')}><div className="flex items-center justify-end gap-1"><Tag className="h-4 w-4 opacity-70 mr-1" />{t('categoryLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('category', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('description')}><div className="flex items-center justify-end gap-1">{t('description')} <span className="sort-icon no-print-in-section">{renderSortIcon('description', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-center invoice-column no-print-in-section">{t('invoice')}</TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('checkNumber')}><div className="flex items-center justify-end gap-1">{t('checkNumberLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('checkNumber', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('type')}><div className="flex items-center justify-end gap-1">{t('type')} <span className="sort-icon no-print-in-section">{renderSortIcon('type', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('amount')}><div className="flex items-center justify-end gap-1">{t('amount')} <span className="sort-icon no-print-in-section">{renderSortIcon('amount', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('date')}><div className="flex items-center justify-end gap-1">{t('dateLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('date', 'expense')}</span></div></TableHead>
                                    </>
                                ) : (
                                    <>
                                        {isEditor && <TableHead className="text-left w-[80px] table-action-col no-print-in-section">{t('actions')}</TableHead>}
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('payee')}><div className="flex items-center gap-1">{t('payeeLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('payee', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('category')}><div className="flex items-center gap-1"><Tag className="h-4 w-4 opacity-70 mr-1" />{t('categoryLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('category', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('description')}><div className="flex items-center gap-1">{t('description')} <span className="sort-icon no-print-in-section">{renderSortIcon('description', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-center invoice-column no-print-in-section">{t('invoice')}</TableHead>
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('checkNumber')}><div className="flex items-center gap-1">{t('checkNumberLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('checkNumber', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('type')}><div className="flex items-center gap-1">{t('type')} <span className="sort-icon no-print-in-section">{renderSortIcon('type', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('amount')}><div className="flex items-center justify-end gap-1">{t('amount')} <span className="sort-icon no-print-in-section">{renderSortIcon('amount', 'expense')}</span></div></TableHead>
                                        <TableHead className="text-left cursor-pointer hover:bg-muted/80" onClick={() => handleExpenseSort('date')}><div className="flex items-center gap-1">{t('dateLabel')} <span className="sort-icon no-print-in-section">{renderSortIcon('date', 'expense')}</span></div></TableHead>
                                    </>
                                )}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                           {filteredAndSortedExpenses.map((expense, index) => {
                                const isUnaccounted = expense.unaccounted;
                                return (
                                    <TableRow
                                        key={expense.id}
                                        className={cn(
                                            "font-medium",
                                            index % 2 === 0 ? 'bg-muted/50' : '',
                                            isUnaccounted && !expenseFilters.accountEverything && "text-orange-600 dark:text-orange-400 opacity-70 hover:opacity-90"
                                        )}
                                    >
                                {language === 'ar' ? (
                                    <>
                                        {isEditor && (<TableCell className="text-left py-1.5 table-action-col no-print-in-section"><Button variant="ghost" size="icon" onClick={() => openEditExpenseDialog(expense)} title={t('edit')} disabled={addExpenseMutation.isPending || updateExpenseMutation.isPending} className="h-8 w-8"><Pencil className="h-4 w-4" /><span className="sr-only">{t('edit')}</span></Button></TableCell>)}
                                        <TableCell className="text-right font-medium py-1.5">{expense.payee || '-'}</TableCell>
                                        <TableCell className="text-right font-medium py-1.5">{expense.category}</TableCell>
                                        <TableCell className="text-right font-medium py-1.5">{expense.description || '-'}</TableCell>
                                        <TableCell className="text-center font-medium py-1.5 invoice-column no-print-in-section">
                                            <div className="flex items-center justify-center gap-1">
                                                <span>{expense.hasInvoice ? t('invoiceYes') : t('invoiceNo')}</span>
                                                {hasCloudinaryAttachments(expense.attachments) ? (
                                                    <>
                                                        {(() => {
                                                            const badgeStyle = getAttachmentBadgeStyle(expense.attachments);
                                                            const badgeDescription = getAttachmentBadgeDescription(expense.attachments);
                                                            return (
                                                                <Badge
                                                                    variant={badgeStyle.variant}
                                                                    className={`h-5 px-1.5 text-xs font-normal ${badgeStyle.className}`}
                                                                    style={badgeStyle.style}
                                                                    title={badgeDescription}
                                                                    aria-label={badgeDescription}
                                                                >
                                                                    {getCloudinaryAttachmentCount(expense.attachments)}
                                                                </Badge>
                                                            );
                                                        })()}
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-6 w-6 text-primary/70 hover:text-primary no-print-in-section"
                                                            onClick={() => openAttachmentManager(expense)}
                                                            title={t('manageAttachments')}
                                                        >
                                                            <Paperclip className="h-4 w-4" />
                                                        </Button>
                                                    </>
                                                ) : (
                                                    !isViewer && (
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-6 w-6 text-muted-foreground/70 hover:text-primary no-print-in-section"
                                                            onClick={() => openAttachmentManager(expense)}
                                                            title={t('addAttachments')}
                                                        >
                                                            <PlusCircle className="h-4 w-4" />
                                                        </Button>
                                                    )
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-right font-medium py-1.5">{expense.checkNumber || '-'}</TableCell>
                                        <TableCell className="text-right font-medium py-1.5">{getTranslatedType(expense.type)}</TableCell>
                                        <TableCell className="text-right font-medium py-1.5 amount-column">
                                            {isUnaccounted && !expenseFilters.accountEverything ? (
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <span className="flex items-center justify-end">
                                                        <AlertCircle className="h-4 w-4 mr-1 text-orange-500" />
                                                        {expense.amount.toLocaleString(language, { style: 'currency', currency: currency })}
                                                    </span>
                                                </TooltipTrigger>
                                                <TooltipContent><p>{t('unaccountedExpense')}</p></TooltipContent>
                                            </Tooltip>
                                            ) : (
                                                expense.amount.toLocaleString(language, { style: 'currency', currency: currency })
                                            )}
                                        </TableCell>
                                        <TableCell className="text-right font-medium py-1.5 print:text-xs">{format(new Date(expense.date), DATE_FORMAT)}</TableCell>
                                    </>
                                ) : (
                                    <>
                                        {isEditor && (<TableCell className="text-left py-1.5 table-action-col no-print-in-section"><Button variant="ghost" size="icon" onClick={() => openEditExpenseDialog(expense)} title={t('edit')} disabled={addExpenseMutation.isPending || updateExpenseMutation.isPending} className="h-8 w-8"><Pencil className="h-4 w-4" /><span className="sr-only">{t('edit')}</span></Button></TableCell>)}
                                        <TableCell className="text-left font-medium py-1.5">{expense.payee || '-'}</TableCell>
                                        <TableCell className="text-left font-medium py-1.5">{expense.category}</TableCell>
                                        <TableCell className="text-left font-medium py-1.5">{expense.description || '-'}</TableCell>
                                        <TableCell className="text-center font-medium py-1.5 invoice-column no-print-in-section">
                                            <div className="flex items-center justify-center gap-1">
                                                <span>{expense.hasInvoice ? t('invoiceYes') : t('invoiceNo')}</span>
                                                {hasCloudinaryAttachments(expense.attachments) ? (
                                                    <>
                                                        {(() => {
                                                            const badgeStyle = getAttachmentBadgeStyle(expense.attachments);
                                                            const badgeDescription = getAttachmentBadgeDescription(expense.attachments);
                                                            return (
                                                                <Badge
                                                                    variant={badgeStyle.variant}
                                                                    className={`h-5 px-1.5 text-xs font-normal ${badgeStyle.className}`}
                                                                    style={badgeStyle.style}
                                                                    title={badgeDescription}
                                                                    aria-label={badgeDescription}
                                                                >
                                                                    {getCloudinaryAttachmentCount(expense.attachments)}
                                                                </Badge>
                                                            );
                                                        })()}
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-6 w-6 text-primary/70 hover:text-primary no-print-in-section"
                                                            onClick={() => openAttachmentManager(expense)}
                                                            title={t('manageAttachments')}
                                                        >
                                                            <Paperclip className="h-4 w-4" />
                                                        </Button>
                                                    </>
                                                ) : (
                                                    !isViewer && (
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-6 w-6 text-muted-foreground/70 hover:text-primary no-print-in-section"
                                                            onClick={() => openAttachmentManager(expense)}
                                                            title={t('addAttachments')}
                                                        >
                                                            <PlusCircle className="h-4 w-4" />
                                                        </Button>
                                                    )
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-right font-medium py-1.5 amount-column">
                                            {isUnaccounted && !expenseFilters.accountEverything ? (
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <span className="flex items-center justify-end">
                                                        <AlertCircle className="h-4 w-4 mr-1 text-orange-500" />
                                                        {expense.amount.toLocaleString(language, { style: 'currency', currency: currency })}
                                                    </span>
                                                </TooltipTrigger>
                                                <TooltipContent><p>{t('unaccountedExpense')}</p></TooltipContent>
                                            </Tooltip>
                                            ) : (
                                                expense.amount.toLocaleString(language, { style: 'currency', currency: currency })
                                            )}
                                        </TableCell>
                                        <TableCell className="text-left font-medium py-1.5 print:text-xs">{format(new Date(expense.date), DATE_FORMAT)}</TableCell>
                                    </>
                                )}
                                    </TableRow>
                                );
                            })}

                        <TableRow className="font-bold bg-secondary hover:bg-secondary">
                                {language === 'ar' ? (
                                    <>
                                        {isEditor && <TableCell className="py-1.5"></TableCell>}
                                        <TableCell colSpan={isEditor ? 6 : 5} className={cn("py-1.5 text-left")} > {t('totalFilteredExpenses')} ({filteredAndSortedExpenses.filter(e => expenseFilters.accountEverything || !e.unaccounted).length}) </TableCell>
                                        <TableCell className="text-right py-1.5 amount-column">{filteredAndSortedExpenses.filter(e => expenseFilters.accountEverything || !e.unaccounted).reduce((sum, e) => sum + e.amount, 0).toLocaleString(language, { style: 'currency', currency: currency })}</TableCell>
                                        <TableCell className="py-1.5"></TableCell>
                                    </>
                                ) : (
                                    <>
                                        {isEditor && <TableCell className="py-1.5"></TableCell>}
                                        <TableCell colSpan={isEditor ? 6 : 5} className={cn("py-1.5 text-right")}> {t('totalFilteredExpenses')} ({filteredAndSortedExpenses.filter(e => expenseFilters.accountEverything || !e.unaccounted).length}) </TableCell>
                                        <TableCell className="text-right py-1.5 amount-column">{filteredAndSortedExpenses.filter(e => expenseFilters.accountEverything || !e.unaccounted).reduce((sum, e) => sum + e.amount, 0).toLocaleString(language, { style: 'currency', currency: currency })}</TableCell>
                                        <TableCell className="py-1.5"></TableCell>
                                    </>
                                )}
                            </TableRow>
                        </TableBody>
                    </Table>
                    ) : ( <p className="text-muted-foreground text-center py-4 no-print">{t('noExpensesMatchFilters')}</p> )}
                 </div>
                  {/* Expense Distribution Chart (Web only) */}
                  {expensesByCategory && expensesByCategory.length > 0 && (
                        <Card className="mt-6 no-print">
                            <CardContent>
                                <ChartContainer config={expenseChartConfig} className="mx-auto aspect-square max-h-[300px] sm:max-h-[350px]">
                                    <PieChart>
                                        <ChartTooltip content={<CustomTooltipContent />} />
                                        <Pie
                                            data={expensesByCategory}
                                            dataKey="total"
                                            nameKey="name"
                                            label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }) => {
                                                const RADIAN = Math.PI / 180;
                                                const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
                                                const x = cx + radius * Math.cos(-midAngle * RADIAN);
                                                const y = cy + radius * Math.sin(-midAngle * RADIAN);
                                                if (percent * 100 < 5) return null; // Don't label small slices
                                                return (
                                                    <text
                                                        x={x}
                                                        y={y}
                                                        fill="white"
                                                        textAnchor={x > cx ? 'start' : 'end'}
                                                        dominantBaseline="central"
                                                        className="text-xs font-semibold"
                                                    >
                                                        {`${name} (${(percent * 100).toFixed(0)}%)`}
                                                    </text>
                                                );
                                            }}
                                            labelLine={false}
                                        >
                                            {expensesByCategory.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.fill} />
                                            ))}
                                        </Pie>
                                        <ChartLegend content={<ChartLegendContent nameKey="name" maxItemsPerLine={5}/>} />
                                    </PieChart>
                                </ChartContainer>
                            </CardContent>
                        </Card>
                    )}
              </CardContent>
            </Card>
           </TooltipProvider>
          </TabsContent>

           <TabsContent value="apartments" className="tabs-content no-print">
             <Card className="no-print">
                 <CardHeader className="tab-card-header flex flex-col sm:flex-row items-center justify-end gap-2">
                     {isEditor && (<Button variant="outline" size="sm" onClick={() => setIsAddApartmentOpen(true)} disabled={addApartmentMutation.isPending || updateApartmentMutation.isPending} id="add-apartment-button" className="mt-2 sm:mt-0"><PlusCircle className="mr-2 h-4 w-4" /> {t('addApartment')}</Button>)}
                     <AddApartmentDialog onAddApartment={handleAddApartment} open={isAddApartmentOpen} onOpenChange={setIsAddApartmentOpen} projectId={projectId}/>
                     <AddApartmentDialog onEditApartment={handleEditApartment} initialData={editingApartment} open={isEditApartmentOpen} onOpenChange={setIsEditApartmentOpen} projectId={projectId}/>
                     <AlertDialog open={isDeleteApartmentOpen} onOpenChange={setIsDeleteApartmentOpen}>
                        <AlertDialogContent>
                            <AlertDialogHeader><AlertDialogTitle>{t('confirmApartmentDeletionTitle')}</AlertDialogTitle><AlertDialogDescription>{apartmentToDelete?.status === 'sold' ? t('confirmApartmentDeletionSoldDescription').replace('{number}', apartmentToDelete.number) : t('confirmApartmentDeletionAvailableDescription').replace('{number}', apartmentToDelete?.number || 'N/A')}</AlertDialogDescription></AlertDialogHeader>
                            <AlertDialogFooter>
                                <AlertDialogCancel onClick={() => setApartmentToDelete(null)}>{t('cancelButton')}</AlertDialogCancel>
                                {apartmentToDelete?.status === 'sold' && (<AlertDialogAction onClick={() => handleConfirmDeleteApartment(true)} className={buttonVariants({ variant: "destructive" })} disabled={deleteApartmentMutation.isPending}>{deleteApartmentMutation.isPending ? t('loading')+'...' : t('deleteApartmentAndSaleButton')}</AlertDialogAction>)}
                                {apartmentToDelete?.status !== 'sold' && (<AlertDialogAction onClick={() => handleConfirmDeleteApartment(false)} className={buttonVariants({ variant: "destructive" })} disabled={deleteApartmentMutation.isPending}>{deleteApartmentMutation.isPending ? t('loading')+'...' : t('deleteButton')}</AlertDialogAction>)}
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>
                 </CardHeader>
                 <CardContent>
                      <div className="filters-section flex flex-wrap items-end gap-x-3 gap-y-4 mb-4 p-3 border rounded-lg bg-muted/50">
                          <div className="flex-grow min-w-[100px] space-y-1">
                              <Label htmlFor="filter-floor" className={cn("block", language === 'ar' ? "sm:text-right" : "sm:text-left", isMobile && "text-center")}>{t('floor')}</Label>
                              <Select value={apartmentFilters.floor} onValueChange={(value) => handleApartmentFilterChange('floor', value)}>
                                  <SelectTrigger id="filter-floor"><SelectValue placeholder={t('selectFloorPlaceholder')} /></SelectTrigger>
                                  <SelectContent><SelectItem value="all">{t('allFloors')}</SelectItem>{availableFloors.map(floor => (<SelectItem key={floor} value={String(floor)}>{floor}</SelectItem>))}</SelectContent>
                              </Select>
                          </div>
                          <div className="flex-grow min-w-[220px] space-y-1">
                            <Label className={cn("block", language === 'ar' ? "sm:text-right" : "sm:text-left", isMobile && "text-center")}>{t('status')}</Label>
                              <RadioGroup value={apartmentFilters.status} onValueChange={(value) => handleApartmentFilterChange('status', value as ApartmentStatusFilter)} className="flex space-x-3 pt-2">
                                  <div className="flex items-center space-x-1"><RadioGroupItem value="all" id="status-all" /><Label htmlFor="status-all">{t('allStatuses')}</Label></div>
                                  <div className="flex items-center space-x-1"><RadioGroupItem value="available" id="status-available" /><Label htmlFor="status-available">{t('apartmentStatusAvailable')}</Label></div>
                                  <div className="flex items-center space-x-1"><RadioGroupItem value="sold" id="status-sold" /><Label htmlFor="status-sold">{t('apartmentStatusSold')}</Label></div>
                              </RadioGroup>
                           </div>
                          <Button variant="ghost" size="icon" onClick={resetApartmentFilters} title={t('resetFilters')} className="self-end h-8 w-8"><XCircle className="h-5 w-5 text-muted-foreground" /><span className="sr-only">{t('resetFilters')}</span></Button>
                      </div>

                    {filteredAndSortedApartments.length > 0 ? (
                         <Table>
                             <TableHeader>
                                 <TableRow>
                                     
                                     <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleApartmentSort('number')}><div className="flex items-center justify-end gap-1">{t('number')} <span className="sort-icon">{renderSortIcon('number', 'apartment')}</span></div></TableHead>
                                    <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleApartmentSort('type')}><div className="flex items-center justify-end gap-1">{t('type')} <span className="sort-icon">{renderSortIcon('type', 'apartment')}</span></div></TableHead>
                                    <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleApartmentSort('area')}><div className="flex items-center justify-end gap-1">{t('area')} (m²) <span className="sort-icon">{renderSortIcon('area', 'apartment')}</span></div></TableHead>
                                    <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleApartmentSort('floor')}><div className="flex items-center justify-end gap-1">{t('floor')} <span className="sort-icon">{renderSortIcon('floor', 'apartment')}</span></div></TableHead>
                                     <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleApartmentSort('bedrooms')}><div className="flex items-center justify-end gap-1">{t('bedroomsShort')} <span className="sort-icon">{renderSortIcon('bedrooms', 'apartment')}</span></div></TableHead>
                                    <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleApartmentSort('declaredPrice')}><div className="flex items-center justify-end gap-1">{t('declaredPriceShort')} <span className="sort-icon">{renderSortIcon('declaredPrice', 'apartment')}</span></div></TableHead>
                                    <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleApartmentSort('undeclaredPrice')}><div className="flex items-center justify-end gap-1">{t('undeclaredPriceShort')} <span className="sort-icon">{renderSortIcon('undeclaredPrice', 'apartment')}</span></div></TableHead>
                                     <TableHead className="text-right cursor-pointer hover:bg-muted/80" onClick={() => handleApartmentSort('status')}><div className="flex items-center justify-end gap-1">{t('status')} <span className="sort-icon">{renderSortIcon('status', 'apartment')}</span></div></TableHead>
                                     {(isSuperUser || isEditor) && <TableHead className="text-left w-[80px] table-action-col">{t('actions')}</TableHead>}
                                 </TableRow>
                             </TableHeader>
                             <TableBody>
                                 {filteredAndSortedApartments.map((apartment, index) => (
                                     <TableRow key={apartment.id} className={cn(index % 2 === 0 ? 'bg-muted/50' : '', "font-medium")}>
                                        
                                         <TableCell className="text-right font-medium py-1.5">{apartment.number}</TableCell>
                                         <TableCell className="text-right font-medium py-1.5">{getTranslatedApartmentType(apartment.type)}</TableCell>
                                         <TableCell className="text-right font-medium py-1.5">{apartment.area.toLocaleString(language, { minimumFractionDigits: 1, maximumFractionDigits: 2 })}</TableCell>
                                         <TableCell className="text-right font-medium py-1.5">{apartment.floor}</TableCell>
                                         <TableCell className="text-right font-medium py-1.5">{apartment.bedrooms}</TableCell>
                                         <TableCell className="text-right font-medium py-1.5">
                                           {apartment.declaredPrice ? apartment.declaredPrice.toLocaleString(language, { minimumFractionDigits: 0, maximumFractionDigits: 0 }) + ' ' + (project.currency || 'MAD') : '-'}
                                         </TableCell>
                                         <TableCell className="text-right font-medium py-1.5">
                                           {apartment.undeclaredPrice ? apartment.undeclaredPrice.toLocaleString(language, { minimumFractionDigits: 0, maximumFractionDigits: 0 }) + ' ' + (project.currency || 'MAD') : '-'}
                                         </TableCell>
                                         <TableCell className={cn("text-right font-semibold py-1.5", apartment.status === 'sold' ? 'text-destructive' : 'text-green-600')}>{getTranslatedApartmentStatus(apartment.status)}</TableCell>
                                         {(isSuperUser || isEditor) && (<TableCell className="text-left py-1.5 table-action-col"><DropdownMenu><DropdownMenuTrigger asChild><Button variant="ghost" size="icon" className="h-8 w-8"><MoreVertical className="h-4 w-4" /><span className="sr-only">{t('actions')}</span></Button></DropdownMenuTrigger><DropdownMenuContent align="end"><DropdownMenuItem onClick={() => openEditApartmentDialog(apartment)} disabled={updateApartmentMutation.isPending || deleteApartmentMutation.isPending}><Pencil className="mr-2 h-4 w-4" />{t('edit')}</DropdownMenuItem>{isSuperUser && (<><DropdownMenuSeparator /><DropdownMenuItem onClick={() => openDeleteApartmentDialog(apartment)} disabled={deleteApartmentMutation.isPending} className="text-destructive focus:bg-destructive/10 focus:text-destructive"><Trash2 className="mr-2 h-4 w-4" />{t('deleteButton')}</DropdownMenuItem></>)}</DropdownMenuContent></DropdownMenu></TableCell>)}
                                     </TableRow>
                                 ))}
                             </TableBody>
                         </Table>
                     ) : ( <p className="text-muted-foreground text-center py-4">{t('noApartmentsMatchFilters')}</p> )}
                 </CardContent>
             </Card>
           </TabsContent>

           <TabsContent value="sales" className="tabs-content no-print">
                {/* Nested Tabs for Sales Section */}
                <Tabs defaultValue="payments" className="w-full">
                  <TabsList className="grid w-full grid-cols-2 mb-4">
                    <TabsTrigger value="payments">
                      <Banknote className="mr-2 h-4 w-4" />
                      {t('payments') || 'Paiements'}
                    </TabsTrigger>
                    <TabsTrigger value="clients">
                      <Users className="mr-2 h-4 w-4" />
                      {t('clients') || 'Clients'}
                    </TabsTrigger>
                  </TabsList>

                  {/* Payments Sub-tab */}
                  <TabsContent value="payments" className="mt-0">
                    <ClientPaymentTrackingTable
                      clientPayments={project?.clientPayments || []}
                      propertyClientAssociations={project?.propertyClientAssociations || []}
                      clients={project?.clients || []}
                      apartments={project?.apartments || []}
                      onEditPayment={handleEditClientPayment}
                      onDeletePayment={handleDeleteClientPayment}
                      onAddPayment={handleAddClientPayment}
                      onManageAttachments={openPaymentAttachmentManager}
                      isEditor={isEditor}
                      language={language}
                      currency={currency}
                      showAddButton={isEditor}
                      addButtonHandler={handleAddClientPayment}
                    />
                  </TabsContent>

                  {/* Clients Sub-tab */}
                  <TabsContent value="clients" className="mt-0">
                    <ClientManagementTable
                      clients={project?.clients || []}
                      onAddClient={handleAddClient}
                      onEditClient={handleEditClient}
                      onDeleteClient={handleDeleteClient}
                      isEditor={isEditor}
                      projectId={projectId}
                    />


                  </TabsContent>
                </Tabs>
           </TabsContent>



          <TabsContent value="partners" className="tabs-content no-print">
            <Card>
               <CardHeader className="tab-card-header">
                  <CardTitle className="text-lg">{t('contributors') || 'Contributors'}</CardTitle>
                  <CardDescription className="text-sm">
                    {t('contributorsTabDescription') || 'View contributors who have made contributions to this project. To add new contributors, go to the Dashboard.'}
                  </CardDescription>
              </CardHeader>

              {/* Admin-only percentage override panel */}
              {isAdmin && project?.partners && project.partners.length > 0 && (
                <div className="px-6 pb-4">
                  <PartnerPercentageOverridePanel
                    partners={project.partners}
                    currentOverride={percentageOverrideData}
                    onSave={(override) => savePartnerPercentageOverrideMutation.mutateAsync(override)}
                    onDisable={() => disablePartnerPercentageOverrideMutation.mutateAsync()}
                    isLoading={savePartnerPercentageOverrideMutation.isPending || disablePartnerPercentageOverrideMutation.isPending}
                  />
                </div>
              )}

              <CardContent>
                {allContributionsByPartner && allContributionsByPartner.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-right">{t('name')}</TableHead>
                        <TableHead className="text-right">{t('source')}</TableHead>
                        <TableHead className="text-right">{t('phone')}</TableHead>
                        <TableHead className="text-right">{t('totalContribution')}</TableHead>
                        <TableHead className="text-right">
                          <div className={cn(
                            "flex items-center gap-1",
                            language === 'ar' ? "flex-row-reverse justify-start" : "justify-end"
                          )}>
                            {t('percentage')}
                            {percentageOverrideData?.enabled && (
                              <div className="flex items-center gap-1">
                                <Settings className="h-3 w-3 text-blue-600" />
                                <span className="text-xs text-blue-600 font-medium">
                                  {t('manual') || 'Manual'}
                                </span>
                              </div>
                            )}
                          </div>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                       {allContributionsByPartner.map((partnerData, index) => {
                           // Extract partner name without project info
                           const partnerName = partnerData.isSourceProject
                               ? partnerData.name.split(' (')[0]
                               : partnerData.name;

                           // Find local partner info for phone number
                           const localPartner = project.partners?.find(p => p.name === partnerName);

                           return (
                               <TableRow key={`${partnerName}-${partnerData.sourceProjectName || 'local'}-${index}`}
                                         className={cn(partnerData.total === 0 ? "text-muted-foreground" : "",
                                                      index % 2 === 0 ? 'bg-muted/50' : '',
                                                      "font-medium")}>
                                   <TableCell className="text-right font-medium py-1.5">{partnerName}</TableCell>
                                   <TableCell className="text-right font-medium py-1.5 text-xs">
                                       {partnerData.isSourceProject ? partnerData.sourceProjectName : t('currentProject')}
                                   </TableCell>
                                   <TableCell className="text-right font-medium py-1.5">
                                       {localPartner?.phone ?? (partnerData.isSourceProject ? '-' : '-')}
                                   </TableCell>
                                   <TableCell className="text-right font-medium py-1.5 amount-column">
                                       {partnerData.total.toLocaleString(language, { style: 'currency', currency: currency })}
                                   </TableCell>
                                   <TableCell className="text-right font-semibold py-1.5" style={partnerData.fill ? { color: partnerData.fill } : undefined}>
                                       {partnerData.percentage.toFixed(2)}%
                                   </TableCell>
                               </TableRow>
                           );
                       })}
                        <TableRow className="font-bold bg-secondary hover:bg-secondary">
                            <TableCell colSpan={3} className="text-right py-1.5">{t('totalGeneral')}</TableCell>
                            <TableCell className="text-right py-1.5 amount-column">
                                {allContributionsByPartner.reduce((sum, p) => sum + p.total, 0).toLocaleString(language, { style: 'currency', currency: currency })}
                            </TableCell>
                            <TableCell className="text-right py-1.5">100.00%</TableCell>
                        </TableRow>
                    </TableBody>
                  </Table>
                ) : ( <p className="text-muted-foreground text-center py-4">{t('noPartnersLinked')}</p> )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payments" className="tabs-content no-print">
            <Card className="h-full flex flex-col">
              <CardHeader className={cn(
                "tab-card-header flex flex-col sm:flex-row items-center gap-2 flex-shrink-0",
                language === 'ar' ? "sm:justify-start" : "justify-between"
              )}>
                <div className={cn(
                  "flex gap-2",
                  language === 'ar' && "flex-row-reverse"
                )}>
                  {isEditor && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsAddCraftsmanOpen(true)}
                        disabled={addCraftsmanMutation.isPending}
                        className={cn(
                          "mt-2 sm:mt-0",
                          language === 'ar' && "flex-row-reverse"
                        )}
                      >
                        <UserPlus className={cn("h-4 w-4", language === 'ar' ? "ml-2" : "mr-2")} />
                        {t('addCraftsman')}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsAddInterventionOpen(true)}
                        disabled={addInterventionMutation.isPending}
                        className={cn(
                          "mt-2 sm:mt-0",
                          language === 'ar' && "flex-row-reverse"
                        )}
                      >
                        <PlusCircle className={cn("h-4 w-4", language === 'ar' ? "ml-2" : "mr-2")} />
                        {t('addIntervention')}
                      </Button>
                    </>
                  )}
                </div>

                <AddCraftsmanDialog
                  onAddCraftsman={handleAddCraftsman}
                  open={isAddCraftsmanOpen}
                  onOpenChange={setIsAddCraftsmanOpen}
                />
                <AddCraftsmanDialog
                  onEditCraftsman={handleEditCraftsman}
                  initialData={editingCraftsman}
                  open={isEditCraftsmanOpen}
                  onOpenChange={setIsEditCraftsmanOpen}
                />
                <AddInterventionDialog
                  onAddIntervention={handleAddIntervention}
                  open={isAddInterventionOpen}
                  onOpenChange={setIsAddInterventionOpen}
                  craftsmen={project?.craftsmen || []}
                />
                <AddInterventionDialog
                  onEditIntervention={handleEditIntervention}
                  initialData={editingIntervention}
                  open={isEditInterventionOpen}
                  onOpenChange={setIsEditInterventionOpen}
                  craftsmen={project?.craftsmen || []}
                />
              </CardHeader>
              <CardContent className="flex-1">
                {/* Two-column layout: Sidebar + Main Content */}
                <div className={cn(
                  "grid grid-cols-1 lg:grid-cols-[40%_60%] gap-4 min-h-[600px]",
                  language === 'ar' && "lg:grid-cols-[60%_40%]"
                )}>
                  {/* Left Column: Craftsmen Table (40%) */}
                  <div className={cn(
                    "space-y-3",
                    language === 'ar' && "lg:order-2"
                  )}>
                    <h3 className={cn(
                      "text-lg font-semibold",
                      language === 'ar' && "text-right"
                    )}>
                      {t('craftsmen')}
                    </h3>
                    <div className="h-[calc(100vh-400px)] min-h-[500px]">
                      <CraftsmenTable
                        craftsmen={project?.craftsmen || []}
                        onEditCraftsman={openEditCraftsmanDialog}
                        onDeleteCraftsman={handleDeleteCraftsman}
                        onCraftsmanSelect={handleCraftsmanSelect}
                        selectedCraftsmanId={selectedCraftsman?.id}
                        isEditor={isEditor}
                      />
                    </div>
                  </div>

                  {/* Right Column: Interventions + Expenses Tables (60%) */}
                  <div className={cn(
                    "space-y-3 flex flex-col",
                    language === 'ar' && "lg:order-1"
                  )}>
                    {/* Interventions Table */}
                    <div className="space-y-3 flex-shrink-0">
                      <div className={cn(
                        "flex items-center justify-between",
                        language === 'ar' && "flex-row-reverse"
                      )}>
                        <h3 className={cn(
                          "text-lg font-semibold",
                          language === 'ar' && "text-right"
                        )}>
                          {t('interventions')}
                        </h3>
                        {selectedCraftsman && (
                          <div className={cn(
                            "text-sm text-muted-foreground",
                            language === 'ar' && "text-left"
                          )}>
                            {filteredInterventions.length} {t('interventions')} - {selectedCraftsman.name}
                          </div>
                        )}
                      </div>
                      <div>
                        <PaymentTrackingTable
                          interventions={filteredInterventions}
                          expenses={project?.expenses || []}
                          onEditIntervention={openEditInterventionDialog}
                          onDeleteIntervention={handleDeleteIntervention}
                          onInterventionSelect={handleInterventionSelect}
                          selectedInterventionId={selectedIntervention?.id}
                          isEditor={isEditor}
                        />
                      </div>
                    </div>

                    {/* Expenses Table - Allow it to expand naturally */}
                    <div className="space-y-3 flex-1 min-h-0">
                      <ExpenseDetailsTable
                        selectedIntervention={selectedIntervention}
                        expenses={project?.expenses || []}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="adminDocs" className="tabs-content no-print">
            <Card className="no-print">
              <CardContent>
                <ProjectAdminDocuments
                  projectId={projectId}
                  projectName={project?.name || ''}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Attachment Manager */}
        <AttachmentManager
            open={attachmentManagerOpen}
            onOpenChange={setAttachmentManagerOpen}
            attachments={currentExpenseForAttachments?.attachments}
            onAttachmentsChange={handleAttachmentsChange}
            projectId={projectId || ''}
            expenseId={currentExpenseForAttachments?.id}
            checkNumber={currentExpenseForAttachments?.checkNumber}
            readonly={isViewer || !isEditor}
        />

        {/* Payment Attachment Manager */}
        <PaymentAttachmentManager
            open={paymentAttachmentManagerOpen}
            onOpenChange={setPaymentAttachmentManagerOpen}
            attachments={currentPaymentForAttachments?.attachments}
            onAttachmentsChange={handlePaymentAttachmentsChange}
            projectId={projectId || ''}
            apartmentId={currentPaymentForAttachments?.apartmentId || ''}
            checkNumber={currentPaymentForAttachments?.checkNumber}
            readonly={isViewer || !isEditor}
        />

        {/* Missing Documents Dialog */}
        <MissingDocumentsDialog
          open={isMissingDocsDialogOpen}
          onOpenChange={setIsMissingDocsDialogOpen}
          projects={project ? [project] : []}
          mode="project"
          projectName={project?.name}
          expenses={filteredAndSortedExpenses}
        />

        {/* Add Client Payment Dialog */}
        <AddClientPaymentDialog
          open={isAddClientPaymentOpen}
          onOpenChange={setIsAddClientPaymentOpen}
          onAddPayment={handleSubmitClientPayment}
          projectId={projectId}
          clients={project?.clients || []}
          apartments={project?.apartments || []}
          clientPayments={project?.clientPayments || []}
          propertyClientAssociations={project?.propertyClientAssociations || []}
        />

        {/* Edit Client Payment Dialog */}
        <AddClientPaymentDialog
          open={isEditClientPaymentOpen}
          onOpenChange={(open) => {
            setIsEditClientPaymentOpen(open);
            if (!open) {
              // Reset editing state when dialog closes
              setEditingClientPayment(undefined);
            }
          }}
          onEditPayment={handleSubmitEditClientPayment}
          initialData={editingClientPayment}
          projectId={projectId}
          clients={project?.clients || []}
          apartments={project?.apartments || []}
          clientPayments={project?.clientPayments || []}
          propertyClientAssociations={project?.propertyClientAssociations || []}
        />

    </div>
  );
};

function ProjectDetailSkeleton() {
  return (
    <div className="container mx-auto px-3 py-6 animate-pulse">
       <Card className="mb-4">
         <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 flex-grow">
                <Skeleton className="h-7 w-3/4 mb-1" />
                 <Skeleton className="h-8 w-32 mt-1 sm:mt-0" />
            </div>
         </CardHeader>
          <CardContent className="pt-2 border-t">
             <Skeleton className="h-4 w-1/2 mb-3 pt-1" />
             <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-sm">
               <Skeleton className="h-5 w-2/3" />
               <Skeleton className="h-5 w-1/2" />
               <Skeleton className="h-5 w-1/3" />
               <Skeleton className="h-5 w-3/4" />
             </div>
         </CardContent>
       </Card>

       <div className="flex space-x-1 bg-muted p-1 rounded-md mb-4 overflow-x-auto">
          <Skeleton className="h-10 flex-1 rounded-sm min-w-[100px]" />
          <Skeleton className="h-10 flex-1 rounded-sm min-w-[100px]" />
           <Skeleton className="h-10 flex-1 rounded-sm min-w-[100px]" />
           <Skeleton className="h-10 flex-1 rounded-sm min-w-[100px]" />
          <Skeleton className="h-10 flex-1 rounded-sm min-w-[100px]" />
       </div>

        <Card>
           <CardHeader className="flex flex-col sm:flex-row items-center justify-between gap-2">
              <div className="space-y-2 flex-grow"><Skeleton className="h-6 w-32" /><Skeleton className="h-4 w-48" /></div>
              <Skeleton className="h-9 w-24" />
           </CardHeader>
           <div className="flex flex-wrap items-end gap-3 mb-4 p-3 border rounded-lg bg-muted/50 animate-pulse">
               <div className="flex-grow min-w-[150px] space-y-1"><Skeleton className="h-4 w-16" /><Skeleton className="h-10 w-full" /></div>
                <div className="flex-grow min-w-[150px] space-y-1"><Skeleton className="h-4 w-20" /><Skeleton className="h-10 w-full" /></div>
               <div className="flex-grow min-w-[150px] space-y-1"><Skeleton className="h-4 w-20" /><Skeleton className="h-10 w-full" /></div>
               <div className="flex-grow min-w-[150px] space-y-1"><Skeleton className="h-4 w-20" /><Skeleton className="h-10 w-full" /></div>
               <div className="flex-grow min-w-[200px] space-y-1"><Skeleton className="h-4 w-24" /><Skeleton className="h-10 w-full" /></div>
                 <div className="flex-grow min-w-[220px] space-y-1"><Skeleton className="h-4 w-28" /><div className="flex space-x-3 pt-2"><Skeleton className="h-6 w-20" /><Skeleton className="h-6 w-20" /><Skeleton className="h-6 w-20" /></div></div>
               <Skeleton className="h-10 w-10 self-end" />
           </div>
           <CardContent className="space-y-3">
             <div className="space-y-1">
                 {[...Array(3)].map((_, i) => (
                     <div key={i} className="flex justify-between items-center py-0.5 border-b border-muted/50 last:border-b-0">
                         <Skeleton className="h-4 w-16" />
                         <Skeleton className="h-4 w-12" />
                          <Skeleton className="h-4 w-10" />
                         <Skeleton className="h-4 w-8" />
                         <Skeleton className="h-4 w-8" />
                         <Skeleton className="h-4 w-8" />
                         <Skeleton className="h-4 w-16" />
                         <Skeleton className="h-4 w-12" />
                         <Skeleton className="h-4 w-10" />
                     </div>
                 ))}
             </div>
              <Separator />
               <div className="flex justify-end items-center font-bold py-0.5 gap-2"><Skeleton className="h-5 w-28" /><Skeleton className="h-5 w-24" /></div>
           </CardContent>
        </Card>
    </div>
  );
};
