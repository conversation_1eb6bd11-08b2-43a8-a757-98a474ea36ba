import type { Contribution, Partner, IndividualContribution, PartnerPercentageOverride } from '@/types';
import { getProjectById } from '@/lib/firebase/firebaseService';

/**
 * Calculate dynamic ownership percentages based on actual contributions
 * @param projectId - The project ID to calculate ownership for
 * @returns Array of partners with calculated ownership percentages
 */
export async function calculateDynamicOwnershipPercentages(projectId: string): Promise<Array<Partner & { calculatedOwnershipPercentage: number }>> {
  try {
    // Get project data including contributions
    const project = await getProjectById(projectId);
    if (!project || !project.partners || project.partners.length === 0) {
      return [];
    }

    // Use the existing aggregatePartnerContributions function to get accurate totals
    const aggregatedContributions = await aggregatePartnerContributions(project.contributions, project.partners);

    // Calculate total contributions from aggregated data
    const totalContributions = aggregatedContributions.reduce((sum, partner) => sum + partner.totalAmount, 0);

    // Map aggregated data back to partners with calculated ownership percentages
    const partnersWithOwnership = project.partners.map(partner => {
      // Find the partner's contribution in the aggregated data
      const partnerContribution = aggregatedContributions.find(agg =>
        agg.partnerId === partner.id || agg.partnerName === partner.name
      );

      const partnerAmount = partnerContribution?.totalAmount || 0;
      const calculatedOwnershipPercentage = totalContributions > 0
        ? (partnerAmount / totalContributions) * 100
        : 100 / project.partners.length; // Equal distribution if no contributions

      return {
        ...partner,
        calculatedOwnershipPercentage: Math.round(calculatedOwnershipPercentage * 10) / 10 // Round to 1 decimal
      };
    });

    return partnersWithOwnership;

  } catch (error) {
    console.error('❌ Error calculating dynamic ownership:', error);
    return [];
  }
}

/**
 * Calculates individual breakdown for a project-sourced contribution dynamically
 * @param totalAmount - Total contribution amount
 * @param sourceProjectId - ID of the source project
 * @returns Promise resolving to individual contribution breakdown
 */
export const calculateDynamicIndividualBreakdown = async (
  totalAmount: number,
  sourceProjectId: string
): Promise<IndividualContribution[]> => {
  try {
    // Calculate dynamic ownership percentages based on actual contributions
    const partnersWithOwnership = await calculateDynamicOwnershipPercentages(sourceProjectId);

    if (partnersWithOwnership.length === 0) {
      return [];
    }

    // Create breakdown using calculated ownership percentages
    const breakdown = partnersWithOwnership.map(partner => {
      const percentage = partner.calculatedOwnershipPercentage;
      const amount = Math.round((totalAmount * percentage) / 100);

      return {
        partnerId: partner.id,
        partnerName: partner.name,
        percentage,
        amount
      };
    });

    return breakdown;

  } catch (error) {
    console.error('❌ Error calculating dynamic breakdown:', error);
    return [];
  }
};

/**
 * Consolidates partner contributions by combining all sources for each unique partner
 * This is used for pie charts where we want to show total contribution per partner
 * @param contributions - All contributions for a project
 * @param partners - All partners in the project
 * @param percentageOverride - Optional manual percentage override settings
 * @returns Promise resolving to array of consolidated partner contributions with totals and percentages
 */
export const consolidatePartnerContributions = async (
  contributions: Contribution[],
  partners: Partner[],
  percentageOverride?: PartnerPercentageOverride
): Promise<Array<{
  partnerId: string;
  partnerName: string;
  totalAmount: number;
  percentage: number;
  fill: string;
}>> => {
  // First get detailed breakdown
  const detailedContributions = await aggregatePartnerContributions(contributions, partners, percentageOverride);

  // Consolidate by partner name (removing project source indicators)
  const consolidatedMap = new Map<string, {
    partnerId: string;
    partnerName: string;
    totalAmount: number;
    fill: string;
  }>();

  detailedContributions.forEach(contribution => {
    // Extract base partner name (remove project source info like "(Project 001)")
    const basePartnerName = contribution.partnerName.split(' (')[0];

    // Try to find the actual partner ID from the partners list
    const matchingPartner = partners.find(p => p.name === basePartnerName);
    const partnerId = matchingPartner?.id || contribution.partnerId;

    const existing = consolidatedMap.get(basePartnerName);
    if (existing) {
      existing.totalAmount += contribution.totalAmount;
    } else {
      consolidatedMap.set(basePartnerName, {
        partnerId,
        partnerName: basePartnerName,
        totalAmount: contribution.totalAmount,
        fill: contribution.fill
      });
    }
  });

  // Calculate total for percentage calculation
  const totalContributions = Array.from(consolidatedMap.values()).reduce((sum, partner) => sum + partner.totalAmount, 0);

  // Check if manual percentage override is enabled and configured
  const useManualPercentages = percentageOverride?.enabled &&
                               percentageOverride.percentages &&
                               Object.keys(percentageOverride.percentages).length > 0;

  // Convert to final format with percentages
  const result = Array.from(consolidatedMap.values()).map(partner => {
    let percentage: number;
    let totalAmount: number;

    if (useManualPercentages && percentageOverride.percentages[partner.partnerId] !== undefined) {
      // Use manual percentage and calculate proportional amount
      percentage = percentageOverride.percentages[partner.partnerId];
      totalAmount = totalContributions > 0 ? (totalContributions * percentage) / 100 : 0;
    } else if (useManualPercentages) {
      // Partner not in manual override, skip them
      return null;
    } else {
      // Use calculated percentage from actual contributions
      percentage = totalContributions > 0 ? (partner.totalAmount / totalContributions) * 100 : 0;
      totalAmount = partner.totalAmount;
    }

    return {
      partnerId: partner.partnerId,
      partnerName: partner.partnerName,
      totalAmount,
      percentage,
      fill: partner.fill
    };
  }).filter(Boolean) as Array<{
    partnerId: string;
    partnerName: string;
    totalAmount: number;
    percentage: number;
    fill: string;
  }>;

  // Sort by amount (highest first) for better visualization
  return result.sort((a, b) => b.totalAmount - a.totalAmount);
};

/**
 * Aggregates all partner contributions across individual and project-sourced contributions
 * Uses dynamic calculation for project-sourced contributions to ensure real-time accuracy
 * This maintains separate entries for different contribution sources (used for detailed tables)
 * @param contributions - All contributions for a project
 * @param partners - All partners in the project
 * @param percentageOverride - Optional manual percentage override settings
 * @returns Promise resolving to array of aggregated partner contributions with totals and percentages
 */
export const aggregatePartnerContributions = async (
  contributions: Contribution[],
  partners: Partner[],
  percentageOverride?: PartnerPercentageOverride
): Promise<Array<{
  partnerId: string;
  partnerName: string;
  totalAmount: number;
  percentage: number;
  fill: string;
}>> => {
  const partnerTotals = new Map<string, { name: string; amount: number }>();

  // Initialize all destination project partners with 0
  partners.forEach(partner => {
    partnerTotals.set(partner.id, { name: partner.name, amount: 0 });
  });

  // Process all contributions
  for (const contribution of contributions) {
    if (contribution.source.type === 'individual') {
      // Individual contribution - use partnerId (references centralized partner)
      const sourceId = contribution.source.partnerId;
      if (sourceId) {
        const existing = partnerTotals.get(sourceId);
        if (existing) {
          existing.amount += contribution.amount;
        }
      }
    } else if (contribution.source.type === 'project' && contribution.source.projectId) {
      // Project-sourced contribution - calculate breakdown dynamically
      console.log('🔄 Processing project-sourced contribution:', {
        amount: contribution.amount,
        sourceProjectId: contribution.source.projectId,
        sourceProjectName: contribution.source.projectName
      });

      const dynamicBreakdown = await calculateDynamicIndividualBreakdown(
        contribution.amount,
        contribution.source.projectId
      );

      dynamicBreakdown.forEach(breakdown => {
        // Use a unique key that combines partner name and source project info
        const uniqueKey = `${breakdown.partnerId}_${contribution.source.projectId}`;
        const displayName = `${breakdown.partnerName} (${contribution.source.projectName || 'Project'})`;

        const existing = partnerTotals.get(uniqueKey);
        if (existing) {
          existing.amount += breakdown.amount;
        } else {
          partnerTotals.set(uniqueKey, {
            name: displayName,
            amount: breakdown.amount
          });
        }
      });
    }
  }

  // Calculate total contributions
  const totalContributions = Array.from(partnerTotals.values()).reduce((sum, partner) => sum + partner.amount, 0);

  // Check if manual percentage override is enabled and configured
  const useManualPercentages = percentageOverride?.enabled &&
                               percentageOverride.percentages &&
                               Object.keys(percentageOverride.percentages).length > 0;

  // Generate high-contrast colors for partners
  const chartColors = [
    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b',
    '#e377c2', '#7f7f7f', '#bcbd22', '#17becf', '#aec7e8', '#ffbb78',
    '#98df8a', '#ff9896', '#c5b0d5', '#c49c94', '#f7b6d2', '#c7c7c7',
    '#dbdb8d', '#9edae5'
  ];

  // Build result array from all partners (both destination and source project partners)
  const result: Array<{
    partnerId: string;
    partnerName: string;
    totalAmount: number;
    percentage: number;
    fill: string;
  }> = [];

  let colorIndex = 0;
  partnerTotals.forEach((partnerData, partnerId) => {
    if (partnerData.amount > 0 || useManualPercentages) {
      let percentage: number;
      let totalAmount: number;

      if (useManualPercentages && percentageOverride.percentages[partnerId] !== undefined) {
        // Use manual percentage and calculate proportional amount
        percentage = percentageOverride.percentages[partnerId];
        totalAmount = totalContributions > 0 ? (totalContributions * percentage) / 100 : 0;
      } else if (useManualPercentages) {
        // Partner not in manual override, skip them
        return;
      } else {
        // Use calculated percentage from actual contributions
        percentage = totalContributions > 0 ? (partnerData.amount / totalContributions) * 100 : 0;
        totalAmount = partnerData.amount;
      }

      result.push({
        partnerId,
        partnerName: partnerData.name,
        totalAmount,
        percentage,
        fill: chartColors[colorIndex % chartColors.length]
      });
      colorIndex++;
    }
  });

  // Sort by amount (highest first) for better visualization
  return result.sort((a, b) => b.totalAmount - a.totalAmount);
};

/**
 * Gets the display name for a contribution source
 * @param contribution - The contribution to get display name for
 * @param partners - Project-level partners (for backward compatibility with old contributions)
 * @param centralizedPartners - Centralized company-wide partners (optional)
 * @returns Display name for the contribution source
 */
export const getContributionSourceDisplayName = (
  contribution: Contribution,
  partners: Partner[],
  centralizedPartners?: Partner[]
): string => {
  if (contribution.source.type === 'individual' && contribution.source.partnerId) {
    // First try to find in centralized partners (new system)
    if (centralizedPartners && centralizedPartners.length > 0) {
      const centralizedPartner = centralizedPartners.find(p => p.id === contribution.source.partnerId);
      if (centralizedPartner) {
        return centralizedPartner.name;
      }
    }

    // Fall back to project-level partners (backward compatibility for old contributions)
    const partner = partners.find(p => p.id === contribution.source.partnerId);
    return partner?.name || 'Unknown Partner';
  } else if (contribution.source.type === 'project') {
    return contribution.source.projectName || 'Unknown Project';
  }
  return 'Unknown Source';
};

/**
 * Checks if a contribution is project-sourced
 * @param contribution - The contribution to check
 * @returns True if the contribution is project-sourced
 */
export const isProjectSourcedContribution = (contribution: Contribution): boolean => {
  return contribution.source.type === 'project';
};

/**
 * Gets the total amount contributed by a specific partner across all contributions
 * Uses dynamic calculation for project-sourced contributions
 * @param partnerId - The partner ID to calculate total for
 * @param contributions - All contributions for the project
 * @returns Promise resolving to total amount contributed by the partner
 */
export const getPartnerTotalContribution = async (
  partnerId: string,
  contributions: Contribution[]
): Promise<number> => {
  let total = 0;

  for (const contribution of contributions) {
    if (contribution.source.type === 'individual') {
      // Check partnerId (references centralized partner)
      const sourceId = contribution.source.partnerId;
      if (sourceId === partnerId) {
        total += contribution.amount;
      }
    } else if (contribution.source.type === 'project' && contribution.source.projectId) {
      // Calculate dynamic breakdown for project-sourced contributions
      const dynamicBreakdown = await calculateDynamicIndividualBreakdown(
        contribution.amount,
        contribution.source.projectId
      );
      const breakdown = dynamicBreakdown.find(b => b.partnerId === partnerId);
      total += (breakdown?.amount || 0);
    }
  }

  return total;
};

/**
 * Gets individual contributions for destination project partners only (excludes project-sourced contributions)
 * This is used for the Partners tab to show only direct contributions by project partners
 * @param contributions - All contributions for a project
 * @param partners - Partners in the destination project
 * @returns Array of partner contributions with totals and percentages (individual contributions only)
 */
export const getIndividualPartnerContributions = (
  contributions: Contribution[],
  partners: Partner[]
): Array<{
  partnerId: string;
  partnerName: string;
  totalAmount: number;
  percentage: number;
  fill: string;
}> => {
  const partnerTotals = new Map<string, number>();

  // Initialize all partners with 0
  partners.forEach(partner => {
    partnerTotals.set(partner.id, 0);
  });

  // Process only individual contributions (exclude project-sourced contributions)
  contributions.forEach(contribution => {
    if (contribution.source.type === 'individual') {
      // Use partnerId (references centralized partner)
      const sourceId = contribution.source.partnerId;
      if (sourceId) {
        const currentTotal = partnerTotals.get(sourceId) || 0;
        partnerTotals.set(sourceId, currentTotal + contribution.amount);
      }
    }
  });

  // Calculate total individual contributions
  const totalContributions = Array.from(partnerTotals.values()).reduce((sum, amount) => sum + amount, 0);

  // Generate high-contrast colors for partners
  const chartColors = [
    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b',
    '#e377c2', '#7f7f7f', '#bcbd22', '#17becf', '#aec7e8', '#ffbb78',
    '#98df8a', '#ff9896', '#c5b0d5', '#c49c94', '#f7b6d2', '#c7c7c7',
    '#dbdb8d', '#9edae5'
  ];

  // Build result array
  return partners
    .map((partner, index) => {
      const totalAmount = partnerTotals.get(partner.id) || 0;
      const percentage = totalContributions > 0 ? (totalAmount / totalContributions) * 100 : 0;

      return {
        partnerId: partner.id,
        partnerName: partner.name,
        totalAmount,
        percentage,
        fill: chartColors[index % chartColors.length]
      };
    })
    .filter(partner => partner.totalAmount > 0); // Only include partners with contributions
};

/**
 * Validates partner ownership percentages
 * @param partners - Array of partners to validate
 * @returns Object with validation result and error message if any
 */
export const validatePartnerOwnership = (partners: Partner[]): {
  isValid: boolean;
  error?: string;
} => {
  const totalOwnership = partners.reduce((sum, partner) => sum + (partner.ownershipPercentage || 0), 0);
  
  if (totalOwnership === 0) {
    return { isValid: true }; // Equal distribution will be used
  }
  
  if (Math.abs(totalOwnership - 100) > 0.01) { // Allow for small floating point errors
    return {
      isValid: false,
      error: `Total ownership percentage is ${totalOwnership.toFixed(2)}%. It should equal 100%.`
    };
  }
  
  return { isValid: true };
};
